{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": ["source.fixAll.eslint"], "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"], "java.configuration.updateBuildConfiguration": "automatic", "java.saveActions.organizeImports": true, "java.compile.nullAnalysis.mode": "automatic", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable", "explorer.excludeGitIgnore": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json"}, "java.project.explorer.showNonJavaResources": true, "java.dependency.packagePresentation": "hierarchical", "[vue]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "java.debug.settings.onBuildFailureProceed": true, "azureTerraform.checkTerraformCmd": false}