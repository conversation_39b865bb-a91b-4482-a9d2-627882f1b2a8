package com.afakto.batch.bnp;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

import org.junit.jupiter.api.Test;

import com.afakto.domain.enumeration.BankTransactionCategory;

class BnpAccountStatementCategorisationTest {
    private BnpAccountStatementCategorisation bnpAccountStatementCategorisation = new BnpAccountStatementCategorisation();

    @Test
    void testCategorisation() {
        assertThat(bnpAccountStatementCategorisation).isNotNull();
        assertEquals(
                BankTransactionCategory.CESSION,
                bnpAccountStatementCategorisation.getCategory("1001"));
    }

    @Test
    void testWrong() {
        assertThat(bnpAccountStatementCategorisation).isNotNull();
        assertNotEquals(
                BankTransactionCategory.BANK_TRANSFER,
                bnpAccountStatementCategorisation.getCategory("107C"));
    }
}
