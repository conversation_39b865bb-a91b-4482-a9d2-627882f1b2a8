package com.afakto.batch.bnp;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.afakto.domain.Company;
import com.afakto.domain.Contract;
import com.afakto.domain.Datastream;
import com.afakto.domain.enumeration.BankTransactionType;
import com.afakto.repository.ContractRepository;

@ExtendWith(MockitoExtension.class)
class BnpAccountStatementLineTest {
    private BnpAccountStatementCategorisation bnpAccountStatementCategorisation = new BnpAccountStatementCategorisation();

    @Test
    void ignoreSomeData(@Mock ContractRepository contractRepository) {
        when(contractRepository.findOneByCompanyOrgIdAndFactorAccountNumber(any(), any()))
                .thenReturn(Optional.ofNullable(new Contract().setCompany(new Company())));

        var line = new BnpAccountStatementLine().setRecordCode("04")
                .setCurrencyCode("EUR")
                .setNumberOfDecimals(2)
                .setOperationDate("010121")
                .setValueDate("010121")
                .setAmount("2349")
                .setAmountSign('}');

        var bankTransaction = line.toBankTransaction(
                contractRepository,
                bnpAccountStatementCategorisation,
                new Datastream().setOrgId("xxx"));

        assertThat(bankTransaction).isNotNull();
        assertThat(bankTransaction.getAmount()).isEqualTo(new BigDecimal("-234.90"));
        assertThat(bankTransaction.getType()).isEqualTo(BankTransactionType.DEBIT);
    }

    @Test
    void basicBankTransaction(@Mock ContractRepository contractRepository) {
        when(contractRepository.findOneByCompanyOrgIdAndFactorAccountNumber("xxx", "*********"))
                .thenReturn(Optional.ofNullable(new Contract().setCompany(new Company())));

        var line = new BnpAccountStatementLine()
                .setRecordCode("05")
                .setBankCode("30004")
                .setTransactionBNPCode("30004")
                .setBankCounter("000000")
                .setCurrencyCode("EUR")
                .setNumberOfDecimals(2)
                .setAccountNumber("*********")
                .setOperationDate("010121")
                .setValueDate("010121")
                .setAmount("2349")
                .setAmountSign('D');

        var bankTransaction = line.toBankTransaction(
                contractRepository,
                bnpAccountStatementCategorisation,
                new Datastream().setOrgId("xxx"));

        assertThat(bankTransaction).isNotNull();
        assertThat(bankTransaction.getAmount()).isEqualTo(new BigDecimal("234.94"));
        assertThat(bankTransaction.getType()).isEqualTo(BankTransactionType.CREDIT);
    }
}
