package com.afakto.service.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.afakto.domain.PaymentTerms;
import com.afakto.service.dto.PaymentTermsDTO;

class PaymentTermsMapperTest {

    private PaymentTermsMapper paymentTermsMapper;

    @BeforeEach
    void setUp() {
        paymentTermsMapper = new PaymentTermsMapperImpl();
    }

    @Test
    void testMapDtoToEntity() {
        // Create a DTO
        PaymentTermsDTO dto = new PaymentTermsDTO();
        UUID id = UUID.randomUUID();
        dto.setId(id);
        dto.setNumberOfDays(30);

        // Map to entity
        PaymentTerms entity = paymentTermsMapper.toEntity(dto);

        // Verify mapping
        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(dto.getId());
        assertThat(entity.getNumberOfDays()).isEqualTo(dto.getNumberOfDays());
    }

    @Test
    void testMapEntityToDto() {
        // Create an entity
        PaymentTerms entity = new PaymentTerms();
        UUID id = UUID.randomUUID();
        entity.setId(id);
        entity.setNumberOfDays(30);

        // Map to DTO
        PaymentTermsDTO dto = paymentTermsMapper.toDto(entity);

        // Verify mapping
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo(entity.getId());
        assertThat(dto.getNumberOfDays()).isEqualTo(entity.getNumberOfDays());
    }
}
