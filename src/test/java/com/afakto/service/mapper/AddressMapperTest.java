package com.afakto.service.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.afakto.domain.Address;
import com.afakto.service.dto.AddressDTO;

class AddressMapperTest {

    private AddressMapper addressMapper;

    @BeforeEach
    void setUp() {
        addressMapper = new AddressMapperImpl();
    }

    @Test
    void testMapDtoToEntity() {
        // Create a DTO
        AddressDTO dto = new AddressDTO();
        UUID id = UUID.randomUUID();
        dto.setId(id);
        dto.setStreetName("Test Street");
        dto.setStreetNumber("123");
        dto.setCity("Test City");
        dto.setPostalCode("12345");
        dto.setCountry("Test Country");

        // Map to entity
        Address entity = addressMapper.toEntity(dto);

        // Verify mapping
        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(dto.getId());
        assertThat(entity.getStreetName()).isEqualTo(dto.getStreetName());
        assertThat(entity.getStreetNumber()).isEqualTo(dto.getStreetNumber());
        assertThat(entity.getCity()).isEqualTo(dto.getCity());
        assertThat(entity.getPostalCode()).isEqualTo(dto.getPostalCode());
        assertThat(entity.getCountry()).isEqualTo(dto.getCountry());
    }

    @Test
    void testMapEntityToDto() {
        // Create an entity
        Address entity = new Address();
        UUID id = UUID.randomUUID();
        entity.setId(id);
        entity.setStreetName("Test Street");
        entity.setStreetNumber("123");
        entity.setCity("Test City");
        entity.setPostalCode("12345");
        entity.setCountry("Test Country");

        // Map to DTO
        AddressDTO dto = addressMapper.toDto(entity);

        // Verify mapping
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo(entity.getId());
        assertThat(dto.getStreetName()).isEqualTo(entity.getStreetName());
        assertThat(dto.getStreetNumber()).isEqualTo(entity.getStreetNumber());
        assertThat(dto.getCity()).isEqualTo(entity.getCity());
        assertThat(dto.getPostalCode()).isEqualTo(entity.getPostalCode());
        assertThat(dto.getCountry()).isEqualTo(entity.getCountry());
    }
}
