package com.afakto.service.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.afakto.domain.Buyer;
import com.afakto.domain.Invoice;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.InvoiceDTO;

class InvoiceMapperTest {

    private InvoiceMapper invoiceMapper;

    @BeforeEach
    void setUp() {
        invoiceMapper = new InvoiceMapperImpl();
    }

    @Test
    void testMapDtoToEntity() {
        // Create a DTO
        InvoiceDTO dto = new InvoiceDTO();
        UUID id = UUID.randomUUID();
        dto.setId(id);
        dto.setInvoiceNumber("INV-001");
        dto.setDate(LocalDate.now());
        dto.setDueDate(LocalDate.now().plusDays(30));
        dto.setCurrency("EUR");
        dto.setAmount(BigDecimal.valueOf(1000));
        dto.setBalance(BigDecimal.valueOf(1000));
        dto.setType(InvoiceType.INVOICE);

        BuyerDTO buyerDTO = new BuyerDTO();
        buyerDTO.setId(UUID.randomUUID());
        buyerDTO.setCode("BUYER-001");
        dto.setBuyer(buyerDTO);

        // Map to entity
        Invoice entity = invoiceMapper.toEntity(dto);

        // Verify mapping
        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(dto.getId());
        assertThat(entity.getInvoiceNumber()).isEqualTo(dto.getInvoiceNumber());
        assertThat(entity.getDate()).isEqualTo(dto.getDate());
        assertThat(entity.getDueDate()).isEqualTo(dto.getDueDate());
        assertThat(entity.getCurrency()).isEqualTo(dto.getCurrency());
        assertThat(entity.getAmount()).isEqualTo(dto.getAmount());
        assertThat(entity.getBalance()).isEqualTo(dto.getBalance());
        assertThat(entity.getType()).isEqualTo(dto.getType());
        assertThat(entity.getBuyer()).isNotNull();
        assertThat(entity.getBuyer().getId()).isEqualTo(buyerDTO.getId());
    }

    @Test
    void testMapEntityToDto() {
        // Create an entity
        Invoice entity = new Invoice();
        UUID id = UUID.randomUUID();
        entity.setId(id);
        entity.setInvoiceNumber("INV-001");
        entity.setDate(LocalDate.now());
        entity.setDueDate(LocalDate.now().plusDays(30));
        entity.setCurrency("EUR");
        entity.setAmount(BigDecimal.valueOf(1000));
        entity.setBalance(BigDecimal.valueOf(1000));
        entity.setType(InvoiceType.INVOICE);

        Buyer buyer = new Buyer();
        buyer.setId(UUID.randomUUID());
        buyer.setCode("BUYER-001");
        entity.setBuyer(buyer);

        // Map to DTO
        InvoiceDTO dto = invoiceMapper.toDto(entity);

        // Verify mapping
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo(entity.getId());
        assertThat(dto.getInvoiceNumber()).isEqualTo(entity.getInvoiceNumber());
        assertThat(dto.getDate()).isEqualTo(entity.getDate());
        assertThat(dto.getDueDate()).isEqualTo(entity.getDueDate());
        assertThat(dto.getCurrency()).isEqualTo(entity.getCurrency());
        assertThat(dto.getAmount()).isEqualTo(entity.getAmount());
        assertThat(dto.getBalance()).isEqualTo(entity.getBalance());
        assertThat(dto.getType()).isEqualTo(entity.getType());
        assertThat(dto.getBuyer()).isNotNull();
        assertThat(dto.getBuyer().getId()).isEqualTo(buyer.getId());
    }
}
