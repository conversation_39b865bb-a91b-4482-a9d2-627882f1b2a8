package com.afakto.service.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.afakto.domain.Contact;
import com.afakto.service.dto.ContactDTO;

class ContactMapperTest {

    private ContactMapper contactMapper;

    @BeforeEach
    void setUp() {
        contactMapper = new ContactMapperImpl();
    }

    @Test
    void testMapDtoToEntity() {
        // Create a DTO
        ContactDTO dto = new ContactDTO();
        UUID id = UUID.randomUUID();
        dto.setId(id);
        dto.setName("Test Contact");
        dto.setEmail("<EMAIL>");
        dto.setPhone("+1234567890");

        // Map to entity
        Contact entity = contactMapper.toEntity(dto);

        // Verify mapping
        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(dto.getId());
        assertThat(entity.getName()).isEqualTo(dto.getName());
        assertThat(entity.getEmail()).isEqualTo(dto.getEmail());
        assertThat(entity.getPhone()).isEqualTo(dto.getPhone());
    }

    @Test
    void testMapEntityToDto() {
        // Create an entity
        Contact entity = new Contact();
        UUID id = UUID.randomUUID();
        entity.setId(id);
        entity.setName("Test Contact");
        entity.setEmail("<EMAIL>");
        entity.setPhone("+1234567890");

        // Map to DTO
        ContactDTO dto = contactMapper.toDto(entity);

        // Verify mapping
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo(entity.getId());
        assertThat(dto.getName()).isEqualTo(entity.getName());
        assertThat(dto.getEmail()).isEqualTo(entity.getEmail());
        assertThat(dto.getPhone()).isEqualTo(entity.getPhone());
    }
}
