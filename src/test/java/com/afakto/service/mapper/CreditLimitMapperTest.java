package com.afakto.service.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.afakto.domain.CreditLimit;
import com.afakto.service.dto.CreditLimitDTO;

class CreditLimitMapperTest {

    private CreditLimitMapper creditLimitMapper;

    @BeforeEach
    void setUp() {
        creditLimitMapper = new CreditLimitMapperImpl();
    }

    @Test
    void testMapDtoToEntity() {
        // Create a DTO
        CreditLimitDTO dto = new CreditLimitDTO();
        UUID id = UUID.randomUUID();
        dto.setId(id);
        dto.setAmount(BigDecimal.valueOf(10000));
        dto.setCurrency("EUR");

        // Map to entity
        CreditLimit entity = creditLimitMapper.toEntity(dto);

        // Verify mapping
        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(dto.getId());
        assertThat(entity.getAmount()).isEqualTo(dto.getAmount());
        assertThat(entity.getCurrency()).isEqualTo(dto.getCurrency());
    }

    @Test
    void testMapEntityToDto() {
        // Create an entity
        CreditLimit entity = new CreditLimit();
        UUID id = UUID.randomUUID();
        entity.setId(id);
        entity.setAmount(BigDecimal.valueOf(10000));
        entity.setCurrency("EUR");

        // Map to DTO
        CreditLimitDTO dto = creditLimitMapper.toDto(entity);

        // Verify mapping
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo(entity.getId());
        assertThat(dto.getAmount()).isEqualTo(entity.getAmount());
        assertThat(dto.getCurrency()).isEqualTo(entity.getCurrency());
    }
}
