package com.afakto.service.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.afakto.domain.Buyer;
import com.afakto.domain.Company;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.CompanyDTO;

class BuyerMapperTest {

    private BuyerMapper buyerMapper;

    @BeforeEach
    void setUp() {
        buyerMapper = new BuyerMapperImpl();
    }

    @Test
    void testMapDtoToEntity() {
        // Create a DTO
        BuyerDTO dto = new BuyerDTO();
        UUID id = UUID.randomUUID();
        dto.setId(id);
        dto.setCode("TEST-BUYER");

        CompanyDTO companyDTO = new CompanyDTO();
        companyDTO.setId(UUID.randomUUID());
        companyDTO.setCode("TEST-COMPANY");
        dto.setCompany(companyDTO);

        // Map to entity
        Buyer entity = buyerMapper.toEntity(dto);

        // Verify mapping
        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(dto.getId());
        assertThat(entity.getCode()).isEqualTo(dto.getCode());
        assertThat(entity.getCompany()).isNotNull();
        assertThat(entity.getCompany().getId()).isEqualTo(companyDTO.getId());
    }

    @Test
    void testMapEntityToDto() {
        // Create an entity
        Buyer entity = new Buyer();
        UUID id = UUID.randomUUID();
        entity.setId(id);
        entity.setCode("TEST-BUYER");

        Company company = new Company();
        company.setId(UUID.randomUUID());
        company.setCode("TEST-COMPANY");
        entity.setCompany(company);

        // Map to DTO
        BuyerDTO dto = buyerMapper.toDto(entity);

        // Verify mapping
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo(entity.getId());
        assertThat(dto.getCode()).isEqualTo(entity.getCode());
        assertThat(dto.getCompany()).isNotNull();
        assertThat(dto.getCompany().getId()).isEqualTo(company.getId());
    }
}
