{"afaktoApp": {"dashboard": {"companies": "Companies", "factorDebt": "Factor Debt", "factorDebtTooltip": "Total Invoice Amount financed by the Factoring Institution", "history": "Withdrawn Debt", "historyTooltip": "Sum of factor debt withdrawal from the Factoring Institution during the period (from Factor)", "vsLastMonth": "vs. Last Month", "sellInvoices": "Sell Invoices", "availableToSell": "Available to Sell", "availableToSellTooltip": "Invoices not sent to the factor yet with a Total Credit Limit > Outstanding Amount per Buyer", "potentialFunding": "Potential Funding", "potentialFundingTooltip": "Invoices sent to factor but not financed yet", "guaranteeFund": "Guarantee Fund", "guaranteeFundTooltip": "% of the Outstanding amount kept by the bank as a Guarantee (from Factor)", "amountAtRisk": "Amount at Risk", "amountAtRiskTooltip": "Sum of Outstanding Buyer Amount - Sum of Guaranteed Oustanding Amount (from Factor Buyers)", "amountUnsecured": "Unavailable Amount", "amountUnsecuredTooltip": "Invoices' unavailable amounts (from factor invoice data)", "amountFiscalYearFees": "Year to Date Fees", "amountFiscalYearFeesTooltip": "Sum of bank transactions: factoring commission, fees not gtie invoices, funding commission, other fees", "viewDetails": "View Details", "contractSituation": "Contract Situation", "contractSituationTooltip": "Financing Authorization Limit (in contracts) = Factor Debt (from Factor) + Available to Sell + Remaining Auhorized", "excludedBuyers": "Ineligible Buyers", "excludedBuyersTooltip": "Outstanding from excluded or zero-limit buyer", "remainingAuthorized": "Remaining Authorized", "currentOutstanding": "Factor Outstanding", "outstandingAmount": "Outstanding", "unavailableAmount": "Unavailable", "topBuyers": "Total top 10 Buyers", "topUnavailable": "Total top 10 Unavailable", "topBuyersTooltip": "Top buyers per outstanding Amount", "currency": "Select currency"}}}