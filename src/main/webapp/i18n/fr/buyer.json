{"afaktoApp": {"buyer": {"home": {"title": "Acheteurs", "titleCreate": "Ajouter un nouvel acheteur", "refreshListLabel": "Actualiser la liste", "createLabel": "Ajouter un acheteur", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer un acheteur", "search": "Recherche d'acheteur", "notFound": "<PERSON><PERSON><PERSON> acheteur trouvé"}, "created": "Un nouvel acheteur a été créé avec l'identifiant { param }", "updated": "L'acheteur avec l'identifiant { param } a été mis à jour", "deleted": "L'acheteur avec l'identifiant { param } a été supprimé", "delete": {"question": "Êtes-vous certain de vouloir supprimer l'acheteur { id } ?"}, "create": {"basicInformation": "Informations basiques", "enrichTooltip": "Trouvez un acheteur à partir du nom de son entreprise ou de son SIRET pour un remplissage automatique des champs suivants.", "dataEnrichment": "Données en ligne", "companyHint": "L'entreprise à laquelle l'acheteur est lié", "companyExample": "ex: MARCHÉ DE RUNGIS", "countryExample": "ex: FRANCE", "countryHint": "Le pays de l'acheteur", "buyerDetails": "<PERSON>é<PERSON> de l'acheteur", "noMatchingCompany": "Aucune entreprise trouvée", "manuallyFill": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>"}, "detail": {"title": "<PERSON><PERSON><PERSON>", "invoices": "Factures", "insurer": "<PERSON><PERSON><PERSON>", "factor": "Factor"}, "upload": {"title": "Import", "subtitle": "Choose one or more files", "samples": "Samples of supported files", "created": "Acheteurs créées: { count }", "updated": "Acheteurs mis à jour: { count }", "headers": "Exemple d'en-têtes", "headers_help": "La première ligne du fichier doit contenir les en-têtes, leur ordre n'est pas important. Les en-têtes supportés sont:"}, "buyerFromInsurer": {"title": "<PERSON><PERSON><PERSON>", "updatedCreditLimit": "<PERSON>ite de crédit mise à jour", "noChange": "La limite de crédit n'a pas changé", "obsolete": "Cette limite est osbolète, le montant utilisé par afakto est à 0."}, "buyerFromFactor": {"title": "Données factor", "factorCode": "Code factor", "number": "<PERSON><PERSON><PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amountApproved": "<PERSON><PERSON> approuv<PERSON>", "noAmountApproved": "Pas de limite de crédit", "amountOutstanding": "En cours (factor)", "amountFunded": "<PERSON><PERSON><PERSON>", "amountSecured": "Sécurisé", "gauge": "Jauge"}, "id": "ID", "company": "Entreprise", "code": "Code", "name": "Nom", "identifier": "Nom ou Numéro légal", "buyerID": "ID acheteur", "country": "Ville", "currency": "Monnaie", "outstanding": "Solde impayé", "balance": "En cours (client)", "creditLimit": "<PERSON><PERSON>", "numberType": "Type", "number": "Numéro lé<PERSON>", "enrichBuyer": "<PERSON><PERSON><PERSON> l'ache<PERSON>ur", "enrichNotAvailable": {"INCORRECT_NUMBER": "Votre acheteur a un numéro légal invalide"}, "enrichmentErrors": {"NO_CONTRACT": "Vous ne disposez d'aucun contrat vous permettant d'enrichir des acheteurs", "NO_SUPPORTED_CONTRACT": "Vous ne disposez d'aucun contrat compatible à l'enrichissement de vos acheteurs", "SIREN_NAME_NOT_FOUND": "Le nom de l'acheteur ne possède pas de SIREN connu", "SIREN_INVALID": "Le nom SIREN est incorrect", "SIREN_NOT_FOUND": "Le numéro SIREN ne correspond à aucune entreprise connue", "VAT_INVALID": "Le numéro de TVA est incorrect", "VAT_INVALID_COUNTRY": "Le pays du numéro de TVA est incorrect", "NOT_FOUND": "Le numéro de votre acheteur ne correspond à aucune entreprise connue", "COUNTRY_NOT_ALLOWED": "Votre contat ne supporte pas la recherche pour le pays de l'acheteur."}, "enrichedChangesTitle": "Enrichissement", "enrichCountrySelect": "Veuillez sélectionner le pays de l'acheteur", "enrichNewNumber": "Nouvel ID d'acheteur", "enrichPreviousNumber": "Ancien ID d'acheteur", "noChanges": "Pas de changements", "originalAddress": "Adresse initiale", "enrichedAddress": "Nouvelle adresse", "excluded": "Exclu", "excluded_help": "Les factures seront exclues des futures cessions", "exclusionReason": "Raison de l'exclusion", "address": "<PERSON><PERSON><PERSON>", "contact": "Contact", "paymentTerms": "<PERSON><PERSON><PERSON><PERSON>ai<PERSON>", "invoice": "Facture", "creditLimitHistory": "Historique des limites de crédit", "incoherentLimit": "Limite inco<PERSON>e", "incoherentLimit_helper": "La limite de crédit de l'assureur est différente du montant approuvé du factor", "incoherentBalance": "Attention sur encours client", "incoherentBalance_helper": "En cours au dessus de la limite de crédit assureur", "incoherentAmount": "Attention en cours factor", "incoherentAmount_helper": "L'encours factor est au dessus du montant approuvé par le factor", "buyerFromFactorUnknown": "<PERSON><PERSON><PERSON><PERSON> inconnu", "buyerFromFactorUnknown_helper": "Le factor n'a pas d'information à propos de cet acheteur", "incorrectNumber_helper": "L'ID acheteur est invalide", "pickAddress": "Séléctionner une adresse", "numberWithAddress": "Nombre d'acheteurs ayant une adresse: { count }", "numberWithoutAddress": "Nombre d'acheteurs n'ayant pas d'adresse: { count }"}}}