import { i18n } from 'boot/i18n';

/**
 * Gets the current locale from Vue I18n
 * @param {string} [localeOverride] - Optional locale to use instead of the current app locale
 * @returns {string} The locale code
 */
function getCurrentLocale(localeOverride) {
  if (localeOverride) return localeOverride;

  if (i18n.global.locale) {
    if (typeof i18n.global.locale === 'object' && i18n.global.locale.value) {
      return i18n.global.locale.value;
    }
    if (typeof i18n.global.locale === 'string') {
      return i18n.global.locale;
    }
  }

  return 'en'; // Default to English
}

/**
 * Parses a number string using the specified locale
 * @param {string} value - The string to parse
 * @param {string} locale - The locale to use for parsing
 * @returns {number|null} The parsed number or null if parsing failed
 */
function parseWithLocale(value, locale) {
  try {
    const parts = new Intl.NumberFormat(locale).formatToParts(12345.6);
    const group = parts.find(p => p.type === 'group')?.value || ',';
    const decimal = parts.find(p => p.type === 'decimal')?.value || '.';

    // Remove group separators, convert decimal separator to period, and remove spaces
    const normalized = value
      .replace(new RegExp(`\\${group}`, 'g'), '')
      .replace(decimal, '.')
      .replace(/\s/g, '')
      .replace('\u00A0', ''); // Remove non-breaking spaces

    const result = Number(normalized);
    return isNaN(result) ? null : result;
  } catch (e) {
    console.warn(`Error parsing number with locale ${locale}:`, e);
    return null;
  }
}

/**
 * Parses a string value into a number, handling different locale formats
 * Uses Vue I18n and Intl.NumberFormat for consistent locale handling
 *
 * @param {string|number} value - The value to parse
 * @param {string} [localeOverride] - Optional locale to use instead of the current app locale
 * @returns {number} The parsed number
 */
export const parseLocaleNumber = (value, localeOverride) => {
  // If it's null, undefined, or already a number, return it
  if (value === null || value === undefined) return value;
  if (typeof value === 'number') return value;

  // Only process strings
  if (typeof value !== 'string') return Number(value);

  // Get the current locale
  const currentLocale = getCurrentLocale(localeOverride);

  // Try parsing with the current locale first
  const resultWithCurrentLocale = parseWithLocale(value, currentLocale);
  if (resultWithCurrentLocale !== null) {
    return resultWithCurrentLocale;
  }

  // Try with alternative locales
  const alternativeLocales = ['en', 'fr'].filter(locale => locale !== currentLocale);
  for (const locale of alternativeLocales) {
    const result = parseWithLocale(value, locale);
    if (result !== null) {
      return result;
    }
  }

  // If all else fails, try a simple conversion
  return Number(value.replace(/[^\d.-]/g, ''));
};
