<template>
  <section class="col-md col-sm-12 q-mx-auto">
    <q-card v-if="Object.keys(contractSituation).length" class="dashboard-card contract-card">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.contractSituationTooltip') }}</q-tooltip>
      <q-card-section>
        <h2>{{ $t('afaktoApp.dashboard.contractSituation') }}</h2>
      </q-card-section>
      <div class="row">
        <q-card-section class="col-md column justify-between">
          <div v-for="key in orderedKeys" class="card">
            <div class="card-header">
              <q-badge rounded :style="{ backgroundColor: labelToColor[key] }" />
              {{ $t(`afaktoApp.dashboard.${key}`) }}

              <q-icon
                v-if="key === 'availableToSell'"
                class="cursor-pointer"
                name="east"
                @click="
                  $router.push(
                    `/invoices?context=_availableToSell&balance.notEquals=0&hasCover.equals=true&buyerExcluded.equals=false&isUnderFactor.equals=false&dueDate.greaterThanOrEqual=${date.formatDate(new Date(), 'YYYY-MM-DD')}`,
                  )
                "
              />

              <p class="q-my-none q-ml-auto text-neutralHigh">
                {{ getTotalValue(selectedCurrency) ? ((contractSituation[selectedCurrency][key] / getTotalValue(selectedCurrency)) * 100).toFixed(2) : '0.00'
                }}%
              </p>
            </div>

            <h2 class="row justify-end q-pt-sm">
              <q-tooltip>
                {{ $n(contractSituation[selectedCurrency][key] || 0, 'currency', { currency: selectedCurrency }) }}
              </q-tooltip>
              {{
                $n(Math.max(0, contractSituation[selectedCurrency][key] || 0), 'compact', {
                  currency: selectedCurrency,
                  signDisplay: 'negative',
                })
              }}
            </h2>
          </div>
        </q-card-section>
        <q-card-section class="col-md">
          <apexchart
            v-if="series[selectedCurrency]"
            height="100%"
            :key="selectedCurrency"
            :options="options[selectedCurrency]"
            :series="series[selectedCurrency]"
          />
        </q-card-section>
      </div>
    </q-card>
  </section>
</template>

<script setup>
import { api } from 'boot/axios';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import useNotifications from 'src/util/useNotifications';
import { useRouter } from 'vue-router';
import { date } from 'quasar';

const { notifyError } = useNotifications();
const { n, t } = useI18n();
const $router = useRouter();

const labelToColor = {
  currentOutstanding: '#047AFF',
  availableToSell: '#33A68D',
  remainingAuthorized: '#AF52DE',
};
const orderedKeys = ['currentOutstanding', 'availableToSell', 'remainingAuthorized'];

const donutOptions = {
  chart: {
    type: 'donut',
    events: {
      mounted: chart => {
        chart.windowResizeHandler();
      },
    },
  },
  colors: [],
  dataLabels: { enabled: false },
  labels: [],
  legend: { show: false },
  plotOptions: {
    pie: {
      donut: {
        labels: {
          show: true,
          total: {
            show: true,
            formatter: ({ config, globals }) =>
              n(
                globals.seriesTotals.reduce((a, b) => a + b, 0),
                'compact',
                {
                  currency: config.currency,
                  signDisplay: 'negative',
                },
              ),
          },
          value: {
            formatter: (value, { config }) =>
              n(parseFloat(value), 'compact', {
                currency: config.currency,
                signDisplay: 'negative',
              }),
          },
        },
      },
    },
  },
  tooltip: { enabled: false },
};

const contractSituation = ref({});
const options = ref({});
const series = ref({});

function getTotalValue(currency) {
  if (!contractSituation.value[currency]) return 0;

  return orderedKeys.reduce((total, key) => {
    return total + (contractSituation.value[currency][key] || 0);
  }, 0);
}

const props = defineProps({
  companies: { type: Array, default: () => [] },
  selectedCurrency: { type: String },
});

onMounted(async () => {
  try {
    contractSituation.value = (
      await api.get('/api/dashboard/contractSituation', {
        params: { companies: props.companies.map(company => company.id).join(',') },
      })
    ).data;

    Object.entries(contractSituation.value).forEach(([currency, values]) => {
      options.value[currency] = { ...donutOptions, labels: [] };
      options.value[currency].currency = currency;
      series.value[currency] = [];

      Object.entries(values).forEach(([key, amount]) => {
        donutOptions.colors.push(labelToColor[key]);
        options.value[currency].labels.push(t(`afaktoApp.dashboard.${key}`));
        series.value[currency].push(amount);
      });
    });
  } catch (error) {
    notifyError(error);
  }
});
</script>

<style lang="scss" scoped>
.card {
  background-color: $backgroundSecondary;
  border-radius: 4px;
  flex: 1;
  font-weight: bolder;
  margin-top: -8px;
  padding: 20px 10px;

  & + .card {
    margin-top: 16px;
  }
}

.col-md + .col-md {
  padding-bottom: 8px;
  padding-top: 0;
}

.body--dark .card {
  background-color: $backgroundSecondaryDark;
}

.q-badge {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  align-self: center;
}

h2 {
  font-size: 22px;
  line-height: 100%;
}

.card-header {
  display: flex;
  gap: 7px;
  align-items: center;
}
</style>
