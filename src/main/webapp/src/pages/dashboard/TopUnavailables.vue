<template>
  <section class="topBuyers col-md col-sm-12 q-mx-auto">
    <q-card class="dashboard-card row column q-pb-md">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.topBuyersTooltip') }}</q-tooltip>
      <q-card-section class="q-px-lg q-pt-lg q-pb-none">
        <div class="flex justify-between q-pb-md items-baseline">
          <div class="unavailable-tab-header">
            <h2 class="no-margin">{{ $t('afaktoApp.dashboard.unavailableAmount') }}</h2>
            <q-icon
              class="cursor-pointer"
              name="east"
              @click="$router.push('/invoices?context=_unsecured&invoiceFromFactor_amountUnsecured.notEquals=0&isUnderFactor.equals=true')"
            />
          </div>
          <div class="row q-gutter-sm items-baseline">
            <p class="no-margin text-neutralHigher" style="font-size: 18px">
              {{
                amountUnavailable[selectedCurrency] ?
                  ((amountUnavailable[selectedCurrency]?.second || 0) / factorDebt[selectedCurrency].second * 100).toFixed(2) + '%'
                  : '0.00%'
              }}</p>
          <h2 v-if="amountUnavailable[selectedCurrency]">
            {{
              $n(amountUnavailable[selectedCurrency].second, 'compact', {
                currency: selectedCurrency,
                maximumFractionDigits: 2,
                signDisplay: 'negative',
              })
            }}
          </h2>
            <h2 v-else>
              {{ $n(0, 'compact', {
              currency: selectedCurrency,
              maximumFractionDigits: 2,
              signDisplay: 'negative'
            })
              }}
            </h2>
          </div>
        </div>
        <q-list dense>
          <p class="q-py-sm q-px-md no-margin text-bold" style="font-size: 12px">{{ $t('afaktoApp.buyer.home.title') }}</p>
          <q-item v-for="data in topUnavailable[selectedCurrency]" :key="data.buyer" :to="`/buyers/${data.id}`">
            <q-item-section style="font-size: 13px">{{ data.name }}</q-item-section>
            <q-item-section class="amount" side>
              {{ $n(data.amount || 0, 'currency', { currency: data.currency, maximumFractionDigits: 2 }) }}
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
      <div class="flex justify-end q-py-sm q-mt-auto" style="padding-inline: 40px">
        <p class="q-mr-md q-my-none">{{ $t('afaktoApp.dashboard.topUnavailable') }}</p>
        <p class="text-bold q-my-none">
          {{
            $n(topUnavailableTotal, 'compact', {
              currency: selectedCurrency,
              maximumFractionDigits: 2,
              signDisplay: 'negative',
            })
          }}
        </p>
      </div>
    </q-card>
  </section>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref } from 'vue';

import useNotifications from 'src/util/useNotifications';

const { notifyError } = useNotifications();

const topUnavailable = ref({});
const amountUnavailable = ref({});
const factorDebt = ref({});

const props = defineProps({
  companies: {
    type: Array,
    default: () => [],
  },
  selectedCurrency: {
    type: String,
  },
});

const topUnavailableTotal = computed(() => {
  return topUnavailable.value[props.selectedCurrency]?.reduce((sum, buyer) => sum + buyer.amount, 0) || 0;
});

onMounted(async () => {
  try {
    const [topUnavailableRes, amountUnavailableRes, factorDebtRes] = await Promise.all([
      api.get('/api/dashboard/topUnavailables', {
        params: { companies: props.companies.map(company => company.id).join(',') },
      }),
      api.get('/api/dashboard/amountUnsecured', {
        params: { companies: props.companies.map(company => company.id).join(',') },
      }),
      api.get('/api/dashboard/factorDebt', {
        params: { companies: props.companies.map(company => company.id).join(',') }
      })
    ]);
    // Group by currency
    topUnavailable.value = Object.values(topUnavailableRes.data).reduce((acc, curr) => {
      const currency = curr.currency;
      if (!acc[currency]) {
        acc[currency] = [];
      }
      acc[currency].push(curr);
      return acc;
    }, {});
    amountUnavailable.value = amountUnavailableRes.data;
    factorDebt.value = factorDebtRes.data;

    console.log(factorDebt.value);
  } catch (error) {
    notifyError(error);
  }
});
</script>
