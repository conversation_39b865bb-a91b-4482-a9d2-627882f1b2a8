<template>
  <section class="col-md col-sm-12 q-mx-auto">
    <q-card class="dashboard-card situation-card">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.potentialFundingTooltip') }}</q-tooltip>
      <q-card-section class="column full-height justify-between">
        <div class="situation-card-header">
          <p>{{ $t('afaktoApp.dashboard.potentialFunding') }}</p>
          <q-icon class="cursor-pointer" name="east"
            @click="$router.push('/bank-transactions?context=_potentialFunding')" />
        </div>
        <h2 class="text-right">
          {{
            $n(potentialFunding[selectedCurrency]?.second || 0, 'compact', {
              currency: selectedCurrency,
              signDisplay: 'negative',
            })
          }}
        </h2>
      </q-card-section>
    </q-card>
  </section>

  <section class="col-md col-sm-12 q-mx-auto">
    <q-card class="dashboard-card situation-card">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.guaranteeFundTooltip') }}</q-tooltip>
      <q-card-section class="column full-height justify-between">
        <div class="situation-card-header">
          <p>{{ $t('afaktoApp.dashboard.guaranteeFund') }}</p>
          <q-icon class="cursor-pointer" name="east"
            @click="$router.push('/bank-transactions?context=_guaranteeFund&category.in=GUARANTEE_FUND')" />
        </div>
        <div class="column q-gutter-xs">
          <p class="text-right text-neutralHigh q-ma-none">
            {{ factorDebt[selectedCurrency]?.second ? ((guaranteeFund[selectedCurrency]?.second || 0) / factorDebt[selectedCurrency]?.second * 100).toFixed(2) + '%' : '0.00%'
            }}
          </p>
        <h2 class="text-right">
          {{ $n(guaranteeFund[selectedCurrency]?.second || 0, 'compact', {
            currency: selectedCurrency, signDisplay:
              'negative'
          }) }}
        </h2>
        </div>
      </q-card-section>
    </q-card>
  </section>

  <!--
  <section class="col-md col-sm-12 q-mx-auto">
    <q-card>
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.amountAtRiskTooltip') }}</q-tooltip>
      <q-card-section>
        <p class="no-margin">{{ $t('afaktoApp.dashboard.amountAtRisk') }}</p>
        <q-card-section v-for="(values, currency) in amountAtRisk" :key="currency" class="text-center">
          <q-badge v-if="values.first" :class="getClass(values)" floating>
            <q-tooltip>{{ $n(values.first, 'compact', { currency: currency, signDisplay: 'negative' }) }}</q-tooltip>
            <q-icon v-if="values.first < values.second" name="trending_up" />
            <q-icon v-else-if="values.first > values.second" name="trending_down" />
            <q-icon v-else name="trending_flat" />
            {{ $n(((values.second - values.first) / Math.abs(values.first)) * 100, 'percent') }}%
          </q-badge>
          <h5 class="no-margin">
            {{ $n(values.second, 'compact', { currency: currency, signDisplay: 'negative' }) }}
          </h5>
        </q-card-section>
      </q-card-section>
    </q-card>
  </section>
  -->

  <section class="col-md col-sm-12 q-mx-auto">
    <q-card class="dashboard-card situation-card">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.amountFiscalYearFeesTooltip') }}</q-tooltip>
      <q-card-section class="column full-height justify-between">
        <div class="situation-card-header">
          <p>{{ $t('afaktoApp.dashboard.amountFiscalYearFees') }}</p>
          <q-icon class="cursor-pointer" name="east" @click="
            $router.push(
              '/bank-transactions?context=_fiscalYearFees&category.in=OTHER_FEES&category.in=FACTORING_COMMISSION&category.in=FUNDING_COMMISSION&category.in=FEES_NOT_GTIE_INVOICES',
            )
            " />
        </div>
        <div class="column q-gutter-xs">
        <h2 class="text-right">
          {{ $n(amountFiscalYearFees[selectedCurrency]?.second || 0, 'compact', {
            currency: selectedCurrency,
            signDisplay: 'negative'
          }) }}
        </h2>
        </div>
      </q-card-section>
    </q-card>
  </section>

  <section class="col-md col-sm-12 q-mx-auto">
    <q-card class="dashboard-card situation-card">
      <q-tooltip class="bg-primary">{{ $t('afaktoApp.dashboard.excludedBuyersTooltip') }}</q-tooltip>
      <q-card-section class="column full-height justify-between">
        <div class="situation-card-header">
          <p>{{ $t('afaktoApp.dashboard.excludedBuyers') }}</p>
        </div>
        <div class="column q-gutter-xs">
          <p class="text-right text-neutralHigh q-ma-none">
            {{ amountOutstanding[selectedCurrency]?.second ? ((excludedBuyers[selectedCurrency]?.second || 0) / amountOutstanding[selectedCurrency].second * 100).toFixed(2) + '%' : '0.00%'
            }}

          </p>
        <h2 class="text-right">
          {{
            $n(excludedBuyers[selectedCurrency]?.second || 0, 'compact', {
              currency: selectedCurrency,
              signDisplay: 'negative',
            })
          }}
        </h2>
        </div>
      </q-card-section>
    </q-card>
  </section>
</template>

<script setup>
import { api } from 'boot/axios';
import { onMounted, ref } from 'vue';
import { date } from 'quasar';

import { useAuthenticationStore } from 'src/stores/authentication-store';

const availableToSell = ref({});
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const potentialFunding = ref({});
const guaranteeFund = ref({});
const amountUnsecured = ref({});
const amountFiscalYearFees = ref({});
const excludedBuyers = ref({});
const factorDebt = ref({});
const amountOutstanding = ref({});
// const amountAtRisk = ref({});

function getClass(values) {
  return values.first < values.second ? 'positive' : 'negative';
}

const props = defineProps({
  companies: {
    type: Array,
    default: () => [],
  },
  selectedCurrency: {
    type: String,
  },
});

onMounted(async () => {
  availableToSell.value = (
    await api.get('/api/dashboard/availableToSell', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  potentialFunding.value = (
    await api.get('/api/dashboard/potentialFunding', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  guaranteeFund.value = (
    await api.get('/api/dashboard/guaranteeFund', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  amountUnsecured.value = (
    await api.get('/api/dashboard/amountUnsecured', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  amountFiscalYearFees.value = (
    await api.get('/api/dashboard/amountFiscalYearFees', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  excludedBuyers.value = (
    await api.get('/api/dashboard/excludedBuyers', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  factorDebt.value = (
    await api.get('/api/dashboard/factorDebt', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  amountOutstanding.value = (
    await api.get('/api/dashboard/amountOutstanding', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;

  console.log(amountOutstanding.value);

  /*
  amountAtRisk.value = (
    await api.get('/api/dashboard/amountAtRisk', { params: { companies: props.companies.map(company => company.id).join(',') } })
  ).data;
  */
});
</script>

<style lang="scss" scoped>
.situation-card-header {
  display: flex;
  gap: 14px;
  justify-content: space-between;
}
</style>
