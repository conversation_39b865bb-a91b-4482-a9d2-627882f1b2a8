<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.datastream.home.title') }}</h1>

      <q-space />

      <rows-export
        :base-api-url="baseApiUrl"
        class="buttonNeutral"
        :columns="columns"
        :filters="filters"
        :pagination="pagination"
        :visible-columns="visibleColumns"
      />
      <q-btn class="buttonNeutral" icon="o_file_upload" :label="$t('entity.action.import')" to="/datastreams/upload" />
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_event, row) => onRowClick(row)"
    >
      <template #body-cell-orgId="props">
        <q-td v-if="hasRoleAdmin" align="left">
          <q-avatar class="on-left" size="lg">
            <img v-if="props.value?.logoUrl" :alt="props.value?.displayName" :src="props.value?.logoUrl" />
          </q-avatar>
          {{ props.value?.displayName }}
        </q-td>
      </template>
      <template #body-cell-type="props">
        <q-td auto-width>
          <q-icon :name="'o_' + $t(`afaktoApp.DatastreamType.${props.value}_icon`)" size="md" />
          <q-tooltip>{{ $t(`afaktoApp.DatastreamType.${props.value}`) }}</q-tooltip>
          <q-icon v-if="props.row.outgoing" color="secondary" name="o_north" />
          <q-icon v-else color="secondary" name="o_south" />
        </q-td>
      </template>
      <template #body-cell-error="props">
        <q-td auto-width>
          <template v-if="props.value">
            <q-icon color="negative" name="error" size="lg" />
            <q-tooltip>{{ props.value }}</q-tooltip>
          </template>
        </q-td>
      </template>
      <template #body-cell-log="props">
        <q-td auto-width>
          <template v-if="props.value === 'OK'">
            <q-icon color="positive" name="task_alt" size="lg" />
          </template>
          <template v-else-if="props.value === 'HS'">
            <q-icon color="negative" name="unpublished" size="lg" />
          </template>
          <template v-else-if="props.value">
            <q-icon name="warning" size="lg" />
            <q-tooltip>{{ props.value }}</q-tooltip>
          </template>
        </q-td>
      </template>
    </q-table>

    <!-- Show Panel -->
    <show-panel :open="showPanel" @click="closePanel">
      <div v-if="selectedDatastream.id">
        <q-toolbar>
          <h1>{{ $t('afaktoApp.datastream.detail.title') }}</h1>
          <q-space />
          <q-btn
            class="buttonNeutral"
            icon="download"
            @click="downloadFile(selectedDatastream)"
          >
            <q-tooltip>{{ $t(`afaktoApp.DatastreamType.${selectedDatastream.type}`) }}</q-tooltip>
          </q-btn>
          <q-btn
            class="buttonNeutral"
            icon="close"
            @click="closePanel"
          >
            <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
          </q-btn>
        </q-toolbar>

        <q-form greedy>
          <q-card>
            <q-card-section v-if="selectedDatastream.type">
              <q-input
                filled
                :label="$t('afaktoApp.datastream.type')"
                :model-value="$t(`afaktoApp.DatastreamType.${selectedDatastream.type}`)"
                readonly
              >
                <template #prepend>
                  <q-icon :name="$t(`afaktoApp.DatastreamType.${selectedDatastream.type}_icon`)" size="xl" />
                </template>
              </q-input>

              <b-input v-model="selectedDatastream.path" :label="$t('afaktoApp.datastream.path')" readonly />
              <b-input v-model="selectedDatastream.name" :label="$t('afaktoApp.datastream.name')" readonly />
              <b-input v-model="selectedDatastream.inserts" :label="$t('afaktoApp.datastream.inserts')" readonly
                       type="number" />
              <b-input v-model="selectedDatastream.updates" :label="$t('afaktoApp.datastream.updates')" readonly
                       type="number" />
              <b-input v-model="selectedDatastream.deletes" :label="$t('afaktoApp.datastream.deletes')" readonly
                       type="number" />
            </q-card-section>
          </q-card>

          <q-card v-if="selectedDatastream.error || selectedDatastream.failuresCount">
            <q-banner v-if="selectedDatastream.error" class="text-white bg-red">
              {{ selectedDatastream.error }}
              <template #avatar>
                <q-icon name="error" />
              </template>
            </q-banner>

            <q-table
              v-if="selectedDatastream.failuresCount"
              :columns="failureColumns"
              :rows="selectedDatastream.failures"
              :title="$t('afaktoApp.datastream.failures')"
            >
              <template #body="props">
                <q-tr
                  :class="props.row.raw && 'cursor-pointer'"
                  :props="props"
                  @click="props.row.expand = !props.row.expand"
                >
                  <q-td align="right" auto-width>{{ $n(props.row.line) }}</q-td>
                  <q-td>{{ props.row.message }}</q-td>
                  <q-td auto-width>
                    <q-btn
                      v-if="props.row.raw"
                      color="accent"
                      dense
                      :icon="props.row.expand ? 'expand_less' : 'expand_more'"
                      round
                    />
                  </q-td>
                </q-tr>
                <q-tr v-show="props.row.raw && props.row.expand" :props="props">
                  <q-td colspan="3">
                    {{ props.row.raw }}
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </q-card>
        </q-form>

        <entity-meta :entity="selectedDatastream" />
      </div>
    </show-panel>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from '/src/util/filtering';
import { format } from '/src/util/format';
import ShowPanel from 'src/components/ShowPanel.vue';
import useNotifications from 'src/util/useNotifications';
import DatastreamPanelDetails from './DatastreamPanelDetails.vue';
import { exportFile } from 'quasar';

const baseApiUrl = '/api/datastreams';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const { n, t } = useI18n();
const { notifyError } = useNotifications();
const organizations = ref([]);
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();

// Panel state
const showPanel = ref(false);
const selectedDatastreamId = ref(null);
const selectedDatastream = ref({});

const columns = computed(() => [
  {
    align: 'left',
    filter: hasRoleAdmin
      ? {
          field: 'orgId',
          type: 'enum',
          values: organizations.value.map(org => ({
            label: org.displayName,
            value: org.orgId,
          })),
        }
      : false,
    format: (_value, row) => organizations.value.find(org => org.orgId === row.orgId),
    headerClasses: hasRoleAdmin ? '' : 'hidden',
    label: t('afaktoApp.datastream.organization'),
    name: 'orgId',
    sortable: true,
  },
  {
    align: 'left',
    field: 'type',
    filter: {
      optionLabel: item => t(`afaktoApp.DatastreamType.${item}`),
      type: 'select',
      url: '/api/datastreams/types',
    },
    label: t('afaktoApp.datastream.type'),
    name: 'type',
    sortable: true,
  },
  {
    align: 'left',
    field: 'path',
    filter: { type: 'text' },
    label: t('afaktoApp.datastream.path'),
    name: 'path',
    sortable: true,
  },
  {
    align: 'left',
    field: 'name',
    filter: { type: 'text' },
    label: t('afaktoApp.datastream.name'),
    name: 'name',
    sortable: true,
  },
  {
    align: 'center',
    field: 'createdDate',
    format: value => format(value, 'dd/MM/yyyy HH:mm'),
    label: t('global.field.createdDate'),
    name: 'createdDate',
    sortable: true,
  },
  {
    field: 'inserts',
    filter: { type: 'number' },
    format: value => n(value),
    label: t('afaktoApp.datastream.inserts'),
    name: 'inserts',
    sortable: true,
  },
  {
    field: 'updates',
    filter: { type: 'number' },
    format: value => n(value),
    label: t('afaktoApp.datastream.updates'),
    name: 'updates',
    sortable: true,
  },
  {
    field: 'deletes',
    filter: { type: 'number' },
    format: value => n(value),
    label: t('afaktoApp.datastream.deletes'),
    name: 'deletes',
    sortable: true,
  },
  {
    field: 'failuresCount',
    filter: { type: 'number' },
    format: value => n(value),
    label: t('afaktoApp.datastream.failures'),
    name: 'failuresCount',
    sortable: true,
  },
  {
    align: 'left',
    field: 'error',
    filter: { type: 'text' },
    label: t('afaktoApp.datastream.error'),
    name: 'error',
    sortable: true,
  },
  {
    align: 'left',
    field: 'log',
    filter: { type: 'text' },
    label: t('afaktoApp.datastream.log'),
    name: 'log',
    sortable: true,
  },
]);

const failureColumns = computed(() => [
  {
    name: 'Line',
    label: 'Line',
    field: 'line',
    align: 'right'
  },
  {
    name: 'Reason',
    label: 'Reason',
    field: 'message',
    align: 'left',
    sortable: true
  },
  { align: 'right' }
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const rows = ref([]);

// Panel functions
const onRowClick = async (row) => {
  selectedDatastreamId.value = row.id;
  await loadDatastreamDetails(row.id);
  showPanel.value = true;
};

const loadDatastreamDetails = async (id) => {
  try {
    selectedDatastream.value = (await api.get(`${baseApiUrl}/${id}`)).data;
  } catch (error) {
    notifyError(error);
  }
};

const closePanel = () => {
  showPanel.value = false;
  selectedDatastreamId.value = null;
  selectedDatastream.value = {};
};

const downloadFile = async (datastream) => {
  try {
    const response = await api.get(`${baseApiUrl}/${datastream.id}/download`, {
      responseType: 'blob'
    });
    exportFile(datastream.name, response.data);
  } catch (error) {
    notifyError(error);
  }
};

const onRequest = async ({ pagination: { page, rowsPerPage, sortBy, descending } }) => {
  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      ...filtersToQueryParams(filters.value),
    },
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};

onMounted(async () => {
  if (!hasRoleAdmin) return;
  organizations.value = (await api.get('/api/organizations')).data;
});
</script>
