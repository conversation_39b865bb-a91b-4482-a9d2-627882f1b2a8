<template>
  <div v-if="entity.id">
    <!-- Basic Information -->
    <q-list>
      <q-item>
        <q-item-section side>
          <q-item-label class="text-weight-medium">{{ $t('afaktoApp.datastream.type') }}</q-item-label>
        </q-item-section>
        <q-item-section>
          <q-item-label>
            <q-icon :name="$t(`afaktoApp.DatastreamType.${entity.type}_icon`)" class="q-mr-sm" />
            {{ $t(`afaktoApp.DatastreamType.${entity.type}`) }}
          </q-item-label>
        </q-item-section>
      </q-item>

      <q-separator />

      <q-item>
        <q-item-section side>
          <q-item-label class="text-weight-medium">{{ $t('afaktoApp.datastream.path') }}</q-item-label>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ entity.path }}</q-item-label>
        </q-item-section>
      </q-item>

      <q-separator />

      <q-item>
        <q-item-section side>
          <q-item-label class="text-weight-medium">{{ $t('afaktoApp.datastream.name') }}</q-item-label>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ entity.name }}</q-item-label>
        </q-item-section>
      </q-item>

      <q-separator />

      <q-item>
        <q-item-section side>
          <q-item-label class="text-weight-medium">{{ $t('afaktoApp.datastream.inserts') }}</q-item-label>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ $n(entity.inserts || 0) }}</q-item-label>
        </q-item-section>
      </q-item>

      <q-separator />

      <q-item>
        <q-item-section side>
          <q-item-label class="text-weight-medium">{{ $t('afaktoApp.datastream.updates') }}</q-item-label>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ $n(entity.updates || 0) }}</q-item-label>
        </q-item-section>
      </q-item>

      <q-separator />

      <q-item>
        <q-item-section side>
          <q-item-label class="text-weight-medium">{{ $t('afaktoApp.datastream.deletes') }}</q-item-label>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ $n(entity.deletes || 0) }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>

    <!-- Error Information -->
    <div v-if="entity.error || entity.failuresCount" class="q-mt-md">
      <q-banner v-if="entity.error" class="text-white bg-red q-mb-md">
        {{ entity.error }}
        <template #avatar><q-icon name="error" /></template>
      </q-banner>

      <q-card v-if="entity.failuresCount" flat bordered>
        <q-card-section>
          <div class="text-h6">{{ $t('afaktoApp.datastream.failures') }}</div>
        </q-card-section>
        <q-separator />
        <q-list>
          <template v-for="(failure, index) in entity.failures" :key="index">
            <q-item 
              :class="failure.raw && 'cursor-pointer'" 
              @click="failure.expand = !failure.expand"
            >
              <q-item-section side style="min-width: 60px">
                <q-item-label class="text-right">{{ $n(failure.line) }}</q-item-label>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ failure.message }}</q-item-label>
              </q-item-section>
              <q-item-section side v-if="failure.raw">
                <q-btn 
                  color="accent" 
                  dense 
                  :icon="failure.expand ? 'expand_less' : 'expand_more'" 
                  round 
                  size="sm"
                />
              </q-item-section>
            </q-item>
            <q-item v-if="failure.raw && failure.expand">
              <q-item-section>
                <q-item-label class="text-caption text-grey-7">
                  {{ failure.raw }}
                </q-item-label>
              </q-item-section>
            </q-item>
            <q-separator v-if="index < entity.failures.length - 1" />
          </template>
        </q-list>
      </q-card>
    </div>

    <!-- Actions -->
    <div class="q-mt-md">
      <q-btn 
        color="primary" 
        icon="download" 
        :label="$t('entity.action.download')"
        @click="$emit('download', entity)"
        class="full-width"
      />
    </div>

    <!-- Entity Meta -->
    <div class="q-mt-md">
      <entity-meta :entity="entity" />
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import EntityMeta from 'src/pages/subcomponents/EntityMeta.vue';

const { n, t } = useI18n();

defineProps({
  entity: {
    type: Object,
    default: () => ({})
  }
});

defineEmits(['download']);
</script>
