<template>
  <q-dialog :model-value="modelValue" @update:model-value="val => $emit('update:modelValue', val)" persistent>
    <q-card style="min-width: 350px">
      <q-toolbar>
        <div class="text-h6">{{ t('afaktoApp.buyer.enrichedChangesTitle') }}</div>
        <q-space />
        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <q-card-section v-if="showCountrySelector">
        <div class="text-subtitle1 q-mb-sm">{{ t('afaktoApp.buyer.enrichCountrySelect') }}</div>
        <q-list bordered separator>
          <q-item v-for="option in countryOptions" :key="option.code" clickable @click="onCountrySelected(option.code)">
            <q-item-section class="column items-center no-wrap">
              <span :class="`fi fi-${option.code}`"></span>
              <span>{{ t(`afaktoApp.NumberType.${option.code.toUpperCase()}.${props.countryFilter[option.code].type}.label`) }}</span>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-section v-else-if="!enriching">
        <div v-if="changedFields.length">
          <div class="row justify-around items-center q-gutter-md q-mb-md">
            <div class="col">
              <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichPreviousNumber') }}</div>
              <div class="text-body1">{{ props.entity.number }}</div>
              <q-badge color="blue">{{ $t(`afaktoApp.NumberType.${props.entity.numberType}`).replaceAll('_', ' ') }}</q-badge>
            </div>

            <div class="col-auto flex items-center">
              <q-icon name="arrow_forward" size="md" class="text-grey-7" />
            </div>

            <div class="col">
              <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichNewNumber') }}</div>
              <div class="text-body1">{{ tmp.number }}</div>
              <q-badge color="blue"
                >{{ t(`afaktoApp.NumberType.${tmp.address.country.toUpperCase()}.${tmp.numberType}.label`) }}
                <q-tooltip>
                  {{ $t(`afaktoApp.NumberType.${tmp.numberType}`).replaceAll('_', ' ') }}
                </q-tooltip>
              </q-badge>
            </div>
          </div>

          <div class="row justify-around items-stretch q-gutter-md">
            <div class="col">
              <address-comp :model-value="originalAddress" :readonly="true" />
            </div>

            <div class="col-auto flex items-center">
              <q-icon name="arrow_forward" size="xl" class="text-grey-7" />
            </div>

            <div class="col">
              <address-comp :model-value="enrichedAddress" :readonly="false" />
            </div>
          </div>
        </div>
        <div v-else class="text-center text-grey">
          {{ t('afaktoApp.buyer.noChanges') }}
        </div>
      </q-card-section>

      <q-card-section v-else> Loading... </q-card-section>

      <q-card-actions align="center">
        <q-btn
          :label="$t('entity.action.save')"
          type="submit"
          icon="label_important"
          class="buttonBrand"
          label="Save"
          color="primary"
          @click="onSubmit(entity, enrichedAddress)"
          :loading="loading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
const { notifyError, notifyCustomError } = useNotifications();
const { t } = useI18n();
import { onMounted, ref } from 'vue';

import AddressComp from 'pages/subcomponents/AddressComp.vue';
import BuyerService from 'pages/entities/buyer/buyer.service';
import useNotifications from 'src/util/useNotifications';

const emit = defineEmits(['update:modelValue']);
const loading = ref(false);

const closeDialog = () => {
  emit('update:modelValue', false);
};

const props = defineProps({
  modelValue: Boolean,
  entity: Object,
  countryFilter: Object,
  identifierType: Object,
});

const tmp = ref({
  address: { country: '' },
  numberType: props.entity.numberType,
  number: props.entity.number,
  company: props.entity.company,
});
const showCountrySelector = ref(false);
const originalAddress = ref({});
const enrichedAddress = ref({});
const changedFields = ref([]);
const enriching = ref(false);
const countryOptions = ref({});

onMounted(async () => {
  const countryKeys = Object.keys(props.countryFilter);

  if (Object.keys(props.identifierType).length !== 0) {
    tmp.value.address.country = props.identifierType.country;
    tmp.value.numberType = props.identifierType.type;
    searchLegalEntity();
  } else if (countryKeys.length > 1) {
    countryOptions.value = countryKeys.map(code => ({
      code,
      ...props.countryFilter[code],
    }));
    showCountrySelector.value = true;
  } else closeDialog();
});

const onCountrySelected = code => {
  tmp.value.address.country = code;
  tmp.value.numberType = props.countryFilter[code].type;
  showCountrySelector.value = false;
  searchLegalEntity();
};

const searchLegalEntity = () => {
  enriching.value = true;

  BuyerService.search(tmp.value)
    .then(enrichedBuyer => {
      if (enrichedBuyer.data.length === 0) {
        notifyCustomError(t(`afaktoApp.buyer.enrichmentErrors.NOT_FOUND`));
        return closeDialog();
      }
      console.log(enrichedBuyer.data[0]);
      tmp.value.numberType = enrichedBuyer.data[0].numberType;
      tmp.value.number = enrichedBuyer.data[0].number;

      originalAddress.value = props.entity.address || {};
      enrichedAddress.value = enrichedBuyer.data[0].address || {};

      const diffs = [];

      const fields = ['streetName', 'streetNumber', 'postalCode', 'city', 'stateProvince', 'country'];

      if (props.entity.address == null) props.entity.address = { city: '' };

      for (const field of fields) {
        const oldVal = props.entity.address?.[field] ?? '';
        const newVal = enrichedBuyer.data[0].address?.[field] ?? null;

        if (newVal !== null && oldVal !== newVal) diffs.push({ field });
      }

      changedFields.value = diffs;
    })
    .catch(err => {
      notifyError(err);
    })
    .finally(() => (enriching.value = false));
};

const onSubmit = async (entity, enrichedAddress) => {
  loading.value = true;

  entity.address = enrichedAddress;
  entity.numberType = tmp.value.numberType;
  entity.number = tmp.value.number;

  BuyerService.save(entity)
    .catch(error => notifyError(error))
    .finally(() => {
      loading.value = false;
      closeDialog();
    });
};
</script>
