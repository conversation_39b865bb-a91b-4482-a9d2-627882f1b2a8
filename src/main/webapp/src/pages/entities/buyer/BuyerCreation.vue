<template>
  <q-dialog @update:model-value="val => $emit('update:modelValue', val)" persistent>
    <q-card class="column no-wrap" style="min-width: 800px; height: 800px">
      <q-toolbar class="bg-animate q-py-md bg-backgroundSecondary">
        <h5 style="margin: 0">{{ $t('afaktoApp.buyer.home.titleCreate') }}</h5>
        <q-space />

        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <div class="row no-wrap fit">
        <div style="width: 30%; flex-shrink: 0">
          <q-stepper v-model="tab" vertical color="primary" ref="stepper" animated class="fit" style="border-radius: 0px">
            <q-step :name="'main'" prefix="1" :title="t('afaktoApp.buyer.create.basicInformation')" />
            <q-step :name="'dataEnrichment'" prefix="2" :title="t('afaktoApp.buyer.create.dataEnrichment')" />
            <q-step :name="'buyerDetails'" prefix="3" :title="t('afaktoApp.buyer.create.buyerDetails')" />
          </q-stepper>
        </div>

        <q-tab-panels v-model="tab" animated :vertical="$q.platform.is?.desktop" style="width: 100%">
          <q-tab-panel name="main">
            <q-form ref="formRef" style="display: contents">
              <h6 style="margin-top: 1em; margin-bottom: 0">{{ t('afaktoApp.buyer.create.basicInformation') }}</h6>
              <!--                    <p class="text-green">Let's start with some basic information</p>-->
              <!--                    <div class="enrich-tooltip">-->
              <!--                      <p>-->
              <!--                        {{ $t('afaktoApp.buyer.create.enrichTooltip') }}-->
              <!--                      </p>-->
              <!--                    </div>-->
              <div class="q-col-gutter-md">
                <q-select
                  id="company-select"
                  v-model="entity.company"
                  class="q-pa-none"
                  filled
                  hide-bottom-space
                  :options="useAuthenticationStore().account.companies"
                  option-label="name"
                  option-value="id"
                  :label="entity.company ? '' : $t('afaktoApp.buyer.company')"
                  :rules="[required]"
                />

                <b-input
                  v-model="identifier"
                  @update:model-value="checkIdentifierInput"
                  hide-bottom-space
                  :label="$t('afaktoApp.buyer.identifier')"
                />

                <div v-if="identifierType?.label">
                  <q-badge color="blue">{{ identifierType.label }}</q-badge>
                </div>
                <div v-else-if="!identifierType?.label && Object.keys(countryFilter).length === 0">
                  <q-badge color="blue">Search by name</q-badge>
                </div>
                <div v-else>Multiple country detected for this number, please choose one</div>

                <q-select
                  v-model="entity.address.country"
                  emit-value
                  filled
                  hide-bottom-space
                  :label="entity.address.country ? '' : $t('afaktoApp.contract.country')"
                  id="country-select"
                  map-options
                  :options="filteredCountries"
                  option-label="name"
                  option-value="code"
                  @update:model-value="checkCountry"
                  :rules="[required]"
                >
                  <template v-if="entity.address.country" #prepend>
                    <span :class="`fi fi-${entity.address.country}`"></span>
                  </template>
                  <template #option="scope">
                    <q-item v-bind="scope.itemProps" dense>
                      <q-item-section side>
                        <span :class="`fi fi-${scope.opt.code}`" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.name }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
              </div>
            </q-form>
          </q-tab-panel>

          <q-tab-panel name="dataEnrichment">
            <q-form ref="formRef" style="display: contents">
              <div v-if="!searchLegalEntity">
                <div class="row items-center justify-around q-gutter-md q-mb-sm">
                  <div>{{ filteredLegalEntities.length }} companies found</div>
                  <q-input filled dense debounce="300" v-model="postalCodeFilter" label="Filter by Postal Code" style="max-width: 250px" />
                  <q-btn label="Manual" color="secondary" @click="goToNextStep()" />
                </div>

                <q-table :rows="filteredLegalEntities" :columns="columns" row-key="number" flat bordered dense>
                  <template v-slot:body="props">
                    <q-tr :props="props" @click="copyEntity(props.row)" class="cursor-pointer">
                      <q-td v-for="col in props.cols" :key="col.name" :props="props">
                        {{ col.value }}
                      </q-td>
                    </q-tr>
                  </template>
                </q-table>
              </div>
              <div v-else>Loading...</div>
            </q-form>
          </q-tab-panel>

          <q-tab-panel name="buyerDetails">
            <q-form ref="formRef" style="display: contents">
              <b-input v-model="entity.code" class="required" hide-bottom-space :label="$t('afaktoApp.buyer.code')" :rules="[required]" />
              <q-input
                v-model="entity.number"
                class="required"
                filled
                :label="$t('afaktoApp.buyer.number')"
                :rules="[required]"
                unmasked-value
              >
                <template #prepend>
                  <q-select
                    v-model="entity.numberType"
                    option-value="value"
                    option-label="label"
                    emit-value
                    map-options
                    :options="numberTypeOptions"
                    :label="$t('afaktoApp.buyer.numberType')"
                    bg-color="transparent"
                    class="required"
                    :rules="[required]"
                    style="min-width: 12em"
                  />
                </template>
              </q-input>

              <q-toggle v-model="entity.excluded" :label="$t('afaktoApp.buyer.excluded') + ' - ' + $t('afaktoApp.buyer.excluded_help')" />
              <q-icon color="warning" right name="remove_done" size="sm" style="vertical-align: baseline" />
              <q-input
                v-if="entity.excluded"
                v-model="entity.exclusionReason"
                filled
                :label="t('afaktoApp.buyer.exclusionReason')"
                rows="2"
                type="textarea"
              />

              <address-comp v-model="entity.address" />
              <contact-comp v-model="entity.contact" />
            </q-form>
          </q-tab-panel>
        </q-tab-panels>
      </div>

      <div class="q-pa-sm row justify-between items-center q-gutter-sm bg-backgroundSecondary" style="margin-top: auto">
        <q-btn class="buttonNeutral bg-neutralLowest" :label="$t('entity.action.cancel')" to="#" @click="closeDialog" />

        <!-- Right side: step buttons and save -->
        <div class="q-gutter-sm">
          <q-btn class="bg-neutralLowest" label="<" :disable="isFirstStep()" @click="goToPreviousStep" />
          <q-btn class="bg-neutralLowest" label=">" :disable="isLastStep()" @click="goToNextStep" />
          <q-btn :disable="!isLastStep()" @click="goToNextStep" class="buttonBrand" :label="$t('entity.action.save')" type="submit" />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

import AddressComp from 'pages/subcomponents/AddressComp.vue';
import BuyerService from 'pages/entities/buyer/buyer.service';
import ContactComp from 'pages/subcomponents/ContactComp.vue';
import { identifierChecker } from 'src/util/identifierChecker';
import { required } from 'src/components/rules';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import useNotifications from 'src/util/useNotifications';

const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const { t, tm } = useI18n();
const tab = ref(route.query.tab || 'main');
const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  entity: Object,
});

const formRef = ref(null);

const stepNames = ['main', 'dataEnrichment', 'buyerDetails'];

const columns = [
  { name: 'name', label: 'Name', field: 'name', align: 'left' },
  {
    name: 'number type',
    label: 'Number Type',
    field: row => t(`afaktoApp.NumberType.${row.address.country.toUpperCase()}.${row.numberType}.label`),
  },
  { name: 'number', label: 'Number', field: 'number' },
  { name: 'city', label: 'City', field: row => row.address?.city },
  { name: 'street', label: 'Street', field: row => row.address?.streetName },
  { name: 'postalCode', label: 'Postal Code', field: row => row.address?.postalCode },
];

const { identifier, identifierType, countryFilter, filteredCountries, checkIdentifier, checkCountry } = identifierChecker(
  props.entity,
  useI18n(),
);

const checkIdentifierInput = async inputValue => {
  await checkIdentifier(inputValue);
  if (identifierType.value.country) props.entity.address.country = identifierType.value.country;
  else if (Object.keys(countryFilter.value).length > 1) props.entity.address.country = null;
};

const searchLegalEntity = ref(false);
const legalEntities = ref([]);

const getLegalEntity = () => {
  if (!identifier.value) return;

  if (identifierType.value?.type) {
    props.entity.number = identifier.value;
    props.entity.numberType = identifierType.value.type;
  } else {
    props.entity.name = identifier;
    props.entity.number = null;
    props.entity.numberType = null;
  }

  searchLegalEntity.value = true;
  legalEntities.value = [];

  BuyerService.search(props.entity)
    .then(enrichedBuyers => (legalEntities.value = enrichedBuyers.data))
    .catch(err => notifyError(err))
    .finally(() => (searchLegalEntity.value = false));
};

const copyEntity = row => {
  goToNextStep();
  props.entity.address = row.address;
  props.entity.number = row.number;
  props.entity.numberType = row.numberType;
  props.entity.name = row.name;
};

const onSubmit = () => {
  BuyerService.save(props.entity)
    .then(() => closeDialog())
    .catch(error => notifyError(error));
};

const closeDialog = () => emit('update:modelValue', false);

const stepper = ref(null);

const isFirstStep = () => stepNames.indexOf(tab.value) === 0;
const isLastStep = () => stepNames.indexOf(tab.value) === stepNames.length - 1;

async function goToNextStep() {
  if (formRef.value?.validate) {
    const valid = await formRef.value.validate();
    if (!valid) return;
  }

  if (isLastStep()) return onSubmit();

  stepper.value?.next();

  if (tab.value === 'dataEnrichment') getLegalEntity();
}

function goToPreviousStep() {
  if (isFirstStep()) return;

  stepper.value?.previous();
  if (tab.value === 'dataEnrichment') getLegalEntity();
}

const numberTypeOptions = computed(() => {
  const countryCode = props.entity?.address?.country.toUpperCase();
  if (countryCode === null) return [];
  const countryNumberTypes = tm(`afaktoApp.NumberType.${countryCode}`);

  if (!countryNumberTypes || typeof countryNumberTypes !== 'object') return [];

  return Object.entries(countryNumberTypes).map(([key, value]) => ({
    label: value.label,
    value: key,
  }));
});

const postalCodeFilter = ref('');

const filteredLegalEntities = computed(() => {
  if (!postalCodeFilter.value) return legalEntities.value;
  return legalEntities.value.filter(entity => entity.address?.postalCode?.toString().startsWith(postalCodeFilter.value));
});
</script>
