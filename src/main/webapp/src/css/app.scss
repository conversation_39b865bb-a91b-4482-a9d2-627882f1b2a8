// app global css in SCSS form
@use 'sass:color'; // Import Sass color functions

.body--dark {
  .apexcharts-text {
    fill: $container;
  }
  .apexcharts-tooltip,
  .apexcharts-tooltip-title {
    background-color: #333 !important;
    color: $container !important;
    border: 1px solid #555 !important;
  }
  .apexcharts-legend-text {
    color: $container !important;
  }

  .text-positive {
    color: color.adjust($positive, $lightness: 25%) !important;
  }
  .text-negative {
    color: color.adjust($negative, $lightness: 25%) !important;
  }
}

// Mechanism to disable double clicks
body.loading .q-btn {
  pointer-events: none;
  opacity: 0.65;
  transition: opacity 0.3s;
}

main {
  h1 {
    line-height: 2rem;
    font-size: 1.8rem;
    font-weight: bold;
  }
  h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 0;
  }
  h3 {
    font-size: 1.25em;
    margin: 0;
  }

  &.q-pa-md {
    @media (max-width: $breakpoint-sm-max) {
      padding: 0;
    }
  }

  .body--dark & {
    background-color: black;
  }
}

.q-card {
  border: 1px solid $borderSecondary;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px #0000001a !important;

  .body--dark & {
    border: 1px solid $borderSecondaryDark;
  }

  &.q-card--dark {
    background-color: color.adjust($dark-page, $lightness: -10%);
    box-shadow: 0 0 1em #444;
  }
}

.dashboard-card {
  height: 100%;
}

.situation-card {
  min-width: 18em;

  .situation-card-header {
    margin-bottom: 1.6em;
  }

  .card-header {
    font-weight: bolder;
  }

  p {
    font-weight: bold;
    font-size: 16px;
  }

  h2 {
    line-height: 1.6em;
  }
}

.outstanding-tab-header,
.unavailable-tab-header {
  display: flex;
  gap: 10px;
  align-items: center;
}

.topBuyers {
  h2 {
    line-height: 100%;
  }
  .q-item__section {
    color: $neutralHigher;
    border-bottom: 1px solid $borderPrimary;

    .body--dark & {
      color: $neutralHigherDark;
    }
  }

  .q-list--dense > .q-item,
  .q-item--dense {
    padding: 0 16px !important;
  }
}

table.q-table {
  th,
  td {
    &.address_country,
    &.boolean {
      padding: 0;
      .q-icon,
      .fi {
        margin-left: -0.5em;
        margin-right: -0.5em;
      }
    }
    &.numberType {
      padding-right: 0;
    }
    &.type {
      padding-right: 0;
    }
  }
}

.q-table__card {
  color: $neutralHigher !important;
  &.q-table__card--dark {
    color: $neutralHigherDark !important;
  }
}

.q-dark .q-item {
  color: $secondary;
}

.q-item.q-item--active {
  color: $surface !important;
  background-color: $accent;
}

.q-drawer-container {
  .q-item.logo {
    height: 53px !important;
    margin: 0;
    padding-bottom: 0;
    padding-top: 4px;
    h2 {
      font-size: 1.8em;
      margin-left: -12px;
      margin-top: 12px;
    }
  }
  .q-item {
    border-radius: 0.5em;
    margin: 0 4px;
    padding-left: 12.5px;
  }
  .q-item__section--side {
    min-width: auto;
  }
  .q-router-link--exact-active {
    color: $backgroundSecondary !important;
    background-color: $container;

    .body--dark & {
      background-color: $backgroundSecondaryDark !important;
    }
  }

  .q-separator {
    background-color: $secondary;
  }

  .q-drawer {
    position: fixed !important;
    border-right: 1px solid $borderSecondary;
    body.body--dark & {
      border-color: $borderSecondaryDark;
    }
  }

  // on mini drawer
  .q-drawer--mini {
    // show the content
    .q-expansion-item__content {
      display: block;
    }
  }
}

.q-table__card,
.q-tab-panels {
  box-shadow: none;
  background: transparent !important;
}

.q-badge.positive,
.positive .q-chip,
.positive .q-icon {
  color: $positive !important;
  background-color: color.adjust($positive, $lightness: 65%);
}
.q-badge.negative,
.negative .q-chip,
.negative .q-icon {
  color: $negative !important;
  background-color: color.adjust($negative, $lightness: 45%);
}

.q-toolbar {
  & > label.q-field,
  & > .q-toggle {
    border-radius: 0.25em;
    box-shadow: 0 0 1px $neutral;
    padding: 0 0.4em;
  }
  & > .q-toggle {
    max-width: 15em;
  }
  a.close i {
    display: none;
    padding: 10px;
    font-size: x-large;
    margin-left: -1.6em;
    margin-top: -2em;
    position: absolute;
  }
  *:hover + a.close i,
  a.close:hover i {
    display: initial;
  }
  @media (max-width: $breakpoint-sm-max) {
    padding: 0;
  }
}

.enrich-tooltip {
  background-color: $infoLowest;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;

  p {
    margin: 0;
  }

  .body--dark & {
    background-color: $infoLowestDark;
  }
}
// Filter NG look

// Add filter button
.add-filter {
  box-shadow: none !important;

  .q-field__marginal {
    height: 100%;
  }

  .q-field__control,
  .q-field__native {
    height: 26px !important;
    min-height: 26px !important;
    padding: 0;
  }

  .q-field__native {
    color: $neutralHigh !important;
    font-weight: 500;
  }
}

.reset-filter {
  color: $neutralHigh;
  padding: 0;
}
// Filter style
.new-filter {
  background-color: $backgroundTertiary;
  border: 1px solid $neutralHigh;
  border-radius: 9999px !important;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  min-width: 12em;
  max-width: 12em;

  .body--dark & {
    background-color: $neutralHigher;
    border-color: $neutralHigh;
  }

  .q-field {
    font-size: 12px !important;
  }

  .q-field__control {
    height: 36px !important;
    min-height: 30px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    transform: none !important;
    position: relative;
    top: 0;
  }

  // Align the inner field with the control
  .q-field__inner {
    align-items: center;
  }

  // Ensure the input wrapper is properly sized
  //.q-field__input {
  //  min-height: 20px !important;
  //  line-height: 20px !important;
  //}

  // Center the label and prevent movement on hover

  //.q-field__label {
  //text-align: center;
  //transform: none !important;
  //  font-size: 12px !important;
  //  font-weight: bold;
  //  top: 0 !important;
  //  right: 0 !important;
  //  margin: 0 auto !important;
  //  pointer-events: none;
  //  position: absolute;
  //  line-height: 30px !important;
  //}

  // Adjust content positioning to accommodate centered label
  //.q-field__native {
  //  padding-top: 0 !important;
  //  padding-left: 0 !important;
  //  padding-right: 0 !important;
  //  min-height: 30px !important;
  //}

  // Adjust content to ensure label visibility
  //.q-field__native {
  //  text-align: center;
  //}

  //.q-field__append {
  //  //height: 30px !important;
  //  align-items: center;
  //  padding: 0 4px 0 0 !important;
  //
  //  .q-icon {
  //    font-size: 16px !important;
  //  }
  //}

  // Prevent label from moving on focus/hover
  //&.q-field--float .q-field__label {
  //  transform: none !important;
  //}
}

// Active filter style
.new-filter-active {
  background-color: $infoLowest !important;
  font-size: 12px;
  border: 1px solid $infoMedium !important;

  .q-field__label {
    color: $infoMedium !important;
    font-weight: bold;
  }

  .q-field__native,
  .q-icon {
    color: $infoMedium !important;
  }
  .body--dark & {
    border: 1px solid $infoMedium !important;
    background-color: $infoLowestDark !important;
  }
}

// Buttons look

.q-btn {
  border-radius: 4px;
  font-size: 12px;
}

.buttonBrand,
.buttonNeutral {
  padding: 6px 8px;

  .q-btn__content {
    display: flex;
    align-items: center;
    gap: 6px !important;
  }

  .q-icon {
    font-size: 16px;
    margin-right: 0;
  }

  .block {
    line-height: 16px;
  }
}

.buttonBrand {
  background-color: $brandMedium !important;

  .q-icon {
    color: white;
  }

  .block {
    color: white;
  }
}

.buttonNeutral {
  border: solid 1px $borderSecondary !important;

  .q-field__control,
  .q-field__native,
  .q-field__prepend,
  .q-field__append {
    height: auto !important;
    min-height: 26px !important;
    padding: 0;
  }

  .body--dark & {
    border: solid 1px $borderSecondaryDark !important;
    color: white !important;
  }
}

.q-btn:before {
  box-shadow: none;
}

.buttonDiv {
  display: flex;
  gap: 20px;

  .btn-separator {
    width: 1px;
    background-color: $borderPrimary;
  }
}

.q-dialog .q-card button {
  @extend .buttonNeutral;

  &:last-child {
    @extend .buttonBrand;
  }
}

.enrichButton {
  margin: 0 -12px;
  width: auto;
  padding: 0 12px;

  .q-icon {
    background-color: $brandMedium;
    color: white;
    border-radius: 4px;
    font-size: 16px;
    padding: 2px;
  }
}

.fixed-bottom-left {
  z-index: 1;
}
.entity-meta {
  color: $secondary;
  font-size: smaller;
  .created {
    text-align: right;
  }
}
.comments {
  font-size: smaller;
  max-height: 16em;
  max-width: 30em;
  overflow: auto;
  .created {
    color: $secondary;
    text-align: right;
  }
  .q-card {
    box-shadow: 0.3em 0.3em 0.5em gray;
    padding: 0 1em;
    .q-field {
      .q-field__label {
        font-size: inherit;
      }
      textarea {
        padding: 0;
      }
      .q-field__append {
        padding-left: 0;
        padding-top: 7px;
      }
    }
    .q-card__section {
      white-space: pre-wrap;
      padding: 0.4em 0;
    }
  }

  &:hover {
    z-index: 1;
  }

  @media (max-width: $breakpoint-sm-max) {
    max-height: initial;
    max-width: 99%;
    position: static;
  }
}

// Country flag styling
.fi, // Flag icon class
.flag-icon, // Alternative flag icon class
[class^="flag-icon-"] {
  border-radius: 4px;
}
