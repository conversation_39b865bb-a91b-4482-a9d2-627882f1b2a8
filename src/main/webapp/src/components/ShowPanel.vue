<!-- components/Shared/ShowPanel.vue -->
<template>
  <q-drawer
    side="right"
    show-if-above
    :model-value="open"
    behavior="desktop"
    :width="panelWidth"
    overlay
  >
    <div class="q-pa-md">
      <!-- Header with title and close button -->
      <div v-if="title" class="row items-center q-mb-md">
        <h6 class="q-ma-none">{{ title }}</h6>
        <q-space />
        <q-btn
          flat
          round
          dense
          icon="close"
          @click="$emit('close')"
        />
      </div>

      <!-- Content slot -->
      <slot :entity="entity" />
    </div>
  </q-drawer>
</template>

<script setup>
defineProps({
  open: Boolean,
  panelWidth: {
    type: String,
    default: '22vw'
  },
  title: {
    type: String,
    default: ''
  },
  entity: {
    type: Object,
    default: () => ({})
  }
});

defineEmits(['close']);
</script>
