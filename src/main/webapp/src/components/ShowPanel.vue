<!-- components/Shared/ShowPanel.vue -->
<template>
  <q-drawer
    side="right"
    show-if-above
    :model-value="open"
    behavior="desktop"
    :width="panelWidth"
    overlay
  >
    <div class="q-pa-md">
      <slot />
    </div>
  </q-drawer>
</template>

<script setup>
defineProps({
  open: <PERSON>olean,
  panelWidth: {
    type: String,
    default: 'clamp(20rem, 30vw, 40rem)'
  }
});
</script>
