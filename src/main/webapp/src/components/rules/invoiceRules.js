import { i18n } from 'src/boot/i18n';
import { InvoiceType } from 'src/shared/model/enumerations/invoice-type.model';
import { parseLocaleNumber } from 'src/util/numberUtils';

const { t } = i18n.global;

/**
 * Wrapper around parseLocaleNumber that adds debugging
 */
const ensureNumber = (value) => {
  const result = parseLocaleNumber(value);

  // Log the parsing for debugging
  if (typeof value === 'string') {
    console.log('Parsing number:', { original: value, parsed: result });
  }

  return result;
};

/**
 * Validates that the amount is appropriate for the invoice type
 * - INVOICE: amount must be positive
 * - CREDIT_NOTE: amount must be negative
 * - UNALLOCATED_PAYMENT: amount must be negative
 * - OTHER: can be either positive or negative
 */
export const validAmountForType = (amount, type) => {
  if (amount === null || amount === undefined || type === null || type === undefined) {
    return true; // Skip validation if values aren't provided yet
  }

  // Convert to number, handling both numeric values and formatted strings
  const numAmount = ensureNumber(amount);

  if (isNaN(numAmount)) {
    return t('entity.validation.number');
  }

  switch (type) {
    case InvoiceType.INVOICE:
      return numAmount > 0 || t('afaktoApp.invoice.validation.positiveAmount');
    case InvoiceType.CREDIT_NOTE:
      return numAmount < 0 || t('afaktoApp.invoice.validation.negativeAmount');
    case InvoiceType.UNALLOCATED_PAYMENT:
      return numAmount < 0 || t('afaktoApp.invoice.validation.negativeAmount');
    default:
      return true; // OTHER type can be either positive or negative
  }
};

/**
 * Validates that the balance is appropriate for the invoice type
 * - INVOICE: balance must be positive
 * - CREDIT_NOTE: balance must be negative
 * - UNALLOCATED_PAYMENT: balance must be negative
 * - OTHER: can be either positive or negative
 */
export const validBalanceForType = (balance, type) => {
  if (balance === null || balance === undefined || type === null || type === undefined) {
    return true; // Skip validation if values aren't provided yet
  }

  // Convert to number, handling both numeric values and formatted strings
  const numBalance = ensureNumber(balance);

  if (isNaN(numBalance)) {
    return t('entity.validation.number');
  }

  switch (type) {
    case InvoiceType.INVOICE:
      return numBalance >= 0 || t('afaktoApp.invoice.validation.positiveBalance');
    case InvoiceType.CREDIT_NOTE:
      return numBalance <= 0 || t('afaktoApp.invoice.validation.negativeBalance');
    case InvoiceType.UNALLOCATED_PAYMENT:
      return numBalance <= 0 || t('afaktoApp.invoice.validation.negativeBalance');
    default:
      return true; // OTHER type can be either positive or negative
  }
};

/**
 * Validates that balance is less than or equal to amount in absolute terms
 */
export const balanceLessThanAmount = (balance, amount) => {
  if (balance === null || balance === undefined || amount === null || amount === undefined) {
    return true; // Skip validation if values aren't provided yet
  }

  // Convert to numbers, handling both numeric values and formatted strings
  const numBalance = ensureNumber(balance);
  const numAmount = ensureNumber(amount);

  if (isNaN(numBalance) || isNaN(numAmount)) {
    return true; // Skip validation if values aren't valid numbers
  }

  // Force validation to always fail for testing
  // return false || t('afaktoApp.invoice.validation.balanceExceedsAmount');

  // Ensure the absolute value of balance is less than or equal to the absolute value of amount
  return Math.abs(numBalance) <= Math.abs(numAmount) ||
    t('afaktoApp.invoice.validation.balanceExceedsAmount');
};

/**
 * Validates that amount and balance have the same sign (both positive or both negative)
 * Zero values are allowed and considered valid with any sign
 */
export const sameSignAmountAndBalance = (balance, amount) => {
  if (balance === null || balance === undefined || amount === null || amount === undefined) {
    return true; // Skip validation if values aren't provided yet
  }

  // Convert to numbers, handling both numeric values and formatted strings
  const numBalance = ensureNumber(balance);
  const numAmount = ensureNumber(amount);

  if (isNaN(numBalance) || isNaN(numAmount)) {
    return true; // Skip validation if values aren't valid numbers
  }

  // Skip validation if either value is zero
  if (numBalance === 0 || numAmount === 0) {
    return true;
  }

  // Check if both values have the same sign
  return (Math.sign(numBalance) === Math.sign(numAmount)) ||
    t('afaktoApp.invoice.validation.differentSigns');
};

/**
 * Validates that due date is after invoice date
 */
export const dueDateAfterInvoiceDate = (dueDate, invoiceDate) => {
  if (!dueDate || !invoiceDate) {
    return true; // Skip validation if either date isn't provided yet
  }

  // Convert string dates to Date objects for comparison
  const dueDateObj = new Date(dueDate);
  const invoiceDateObj = new Date(invoiceDate);

  // Check if dates are valid
  if (isNaN(dueDateObj.getTime()) || isNaN(invoiceDateObj.getTime())) {
    return true; // Skip validation if dates are invalid
  }

  // Check if due date is after invoice date
  return dueDateObj >= invoiceDateObj ||
    t('afaktoApp.invoice.validation.dueDateBeforeInvoiceDate');
};
