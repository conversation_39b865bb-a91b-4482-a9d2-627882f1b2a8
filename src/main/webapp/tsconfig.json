{"compilerOptions": {"resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"src/*": ["src/*"], "app/*": ["*"], "components/*": ["src/components/*"], "composable/*": ["src/composable/*"], "shared/*": ["src/shared/*"], "layouts/*": ["src/layouts/*"], "pages/*": ["src/pages/*"], "assets/*": ["src/assets/*"], "boot/*": ["src/boot/*"], "vue$": ["../../../node_modules/vue/dist/vue.runtime.esm-bundler.js"]}}, "exclude": [".quasar"]}