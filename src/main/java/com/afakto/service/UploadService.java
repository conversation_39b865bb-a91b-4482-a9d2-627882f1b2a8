package com.afakto.service;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.domain.Invoice;
import com.afakto.service.dto.DatastreamDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class UploadService {

    private static final int MAX_NUMBER_OF_FAILURES = 999;

    /**
     * Process the uploaded file containing invoice information
     *
     * @param file The CSV file to be uploaded and processed.
     * @return datastream with the results of the upload.
     * @throws IOException
     */
    public DatastreamDTO processFile(MultipartFile file) throws IOException {
        log.info("Processing file: {}", file.getOriginalFilename());

        var current = Instant.now();

        var datastream = new Datastream()
                .setOutgoing(false)
                .setPath("xxx")
                .setName(file.getOriginalFilename());

        Workbook workbook = null;
        // Get the workbook object for Excel file based on file format
        if (file.getOriginalFilename().endsWith(".xlsx")) {
            workbook = new XSSFWorkbook(file.getInputStream());
        } else if (file.getOriginalFilename().endsWith(".xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        }
        if (workbook != null) {
            // Get first sheet from the workbook
            for (var row : workbook.getSheetAt(0)) {
                try {
                    var entity = processRecord(row);
                    if (entity == null)
                        continue;

                    if (entity.getCreatedDate().isAfter(current))
                        datastream.incrementInserts();
                    else if (entity.getLastModifiedDate().isAfter(current))
                        datastream.incrementUpdates();

                    if (datastream.getOrgId() != null)
                        continue;

                    if (entity instanceof Invoice invoice) {
                        datastream.setOrgId(invoice.getBuyer().getCompany().getOrgId());
                    } else if (entity instanceof Buyer buyer) {
                        datastream.setOrgId(buyer.getCompany().getOrgId());
                    }
                } catch (Exception e) {
                    var raw = new ArrayList<String>();
                    for (var cell : row)
                        raw.add(cell.toString());

                    log.info("{} {}: {}", row.getRowNum() + 1, e.getMessage(), raw.toString());

                    datastream.getFailures().add(
                            new DatastreamFailure()
                                    .setDatastream(datastream)
                                    .setLine(row.getRowNum() + 1)
                                    .setMessage(e.getMessage())
                                    .setRaw(raw.toString()));
                    if (row.getRowNum() < 1) {
                        log.warn("Wrong headers");
                        datastream.setError("Wrong headers");
                        break;
                    } else if (datastream.getFailures().size() > MAX_NUMBER_OF_FAILURES) {
                        log.warn("Too many failed lines, aborting processing");
                        datastream.setError("Too many failed lines, aborting processing");
                        break;
                    }
                }
            }
        } else {
            for (var row : createCSVParser(file.getInputStream())) {
                try {
                    var entity = processRecord(row);
                    if (entity == null)
                        continue;

                    if (entity.getVersion() == 0) // the invoice exist, want to update only
                        datastream.incrementInserts();
                    else
                        datastream.incrementUpdates();

                } catch (Exception e) {
                    log.info("{} {}: {}", row.getRecordNumber() + 1, e.getMessage(), row.values());
                    datastream.getFailures().add(
                            new DatastreamFailure()
                                    .setDatastream(datastream)
                                    .setLine((int) row.getRecordNumber() + 1)
                                    .setMessage(e.getMessage())
                                    .setRaw(Arrays.toString(row.values())));
                    if (row.getRecordNumber() < 1) {
                        log.warn("Wrong headers");
                        datastream.setError("Wrong headers");
                        break;
                    } else if (datastream.getFailures().size() > MAX_NUMBER_OF_FAILURES) {
                        log.warn("Too many failed lines, aborting processing");
                        datastream.setError("Too many failed lines, aborting processing");
                        break;
                    }
                }
            }
        }

        return save(datastream);
    }

    /**
     * @param inputStream data
     * @return input stream if everything goes well
     * @throws IOException else throws, IOException
     */
    private CSVParser createCSVParser(InputStream inputStream) throws IOException {
        return CSVFormat.DEFAULT
                .builder()
                .setHeader()
                .setSkipHeaderRecord(true)
                .setIgnoreEmptyLines(true)
                .setIgnoreSurroundingSpaces(true)
                .get()
                .parse(new InputStreamReader(inputStream));
    }

    protected abstract BaseEntity processRecord(Row row);

    protected abstract BaseEntity processRecord(CSVRecord csvRecord);

    protected abstract DatastreamDTO save(Datastream datastream);
}
