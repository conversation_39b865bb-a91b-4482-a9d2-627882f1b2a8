package com.afakto.service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.auth0.exception.Auth0Exception;
import com.auth0.json.mgmt.organizations.Invitation;
import com.auth0.json.mgmt.organizations.Invitee;
import com.auth0.json.mgmt.organizations.Inviter;
import com.auth0.json.mgmt.organizations.Roles;
import com.auth0.json.mgmt.roles.Role;
import com.afakto.security.Auth0Management;
import com.afakto.security.SecurityUtils;
import com.afakto.service.dto.UserDTO;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class Auth0UserMgmt {
    private final Auth0Management auth0Management;

    public void create(UserDTO userDTO) {
        var invitation = new Invitation(
                new Inviter(SecurityUtils.getCurrentUserLogin().orElseThrow()),
                new Invitee(userDTO.getLogin()),
                auth0Management.getTargetClientId());
        invitation.setTtlInSeconds(2592000);

        if (!CollectionUtils.isEmpty(userDTO.getAuthorities()))
            invitation.setRoles(mapToAuth0Roles(userDTO.getAuthorities()));

        try {
            invitation = auth0Management.organizations()
                    .createInvitation(userDTO.getOrgId(), invitation)
                    .execute()
                    .getBody();
            log.info("Invitation created: {}", invitation);
        } catch (Exception e) {
            log.error("Failed to create invitation", e);
            throw new RuntimeException("Failed to create invitation", e);
        }
    }

    // Mostly add or remove roles
    public void update(UserDTO userDTO, Set<String> roles) {
        String userId = auth0Management.getUserId(userDTO.getLogin())
                .orElseThrow(() -> new RuntimeException("User not yet activated"));

        var rolesToDelete = new HashSet<>(roles);
        rolesToDelete.removeAll(userDTO.getAuthorities());
        try {
            if (!rolesToDelete.isEmpty())
                auth0Management.organizations()
                        .deleteRoles(userDTO.getOrgId(), userId, mapToAuth0Roles(rolesToDelete))
                        .execute();
        } catch (Auth0Exception e) {
            log.error("Failed to add roles", e);
            throw new RuntimeException("Failed to add roles", e);
        }

        var rolesToAdd = new HashSet<>(userDTO.getAuthorities());
        rolesToAdd.removeAll(roles);
        try {
            if (!rolesToAdd.isEmpty())
                auth0Management.organizations()
                        .addRoles(userDTO.getOrgId(), userId, mapToAuth0Roles(rolesToAdd))
                        .execute();
        } catch (Auth0Exception e) {
            log.error("Failed to add roles", e);
            throw new RuntimeException("Failed to add roles", e);
        }
    }

    public void delete(String login) {
        auth0Management.getUserId(login)
                .ifPresentOrElse(auth0Management::deleteUser, () -> log.warn("User not found: {}", login));
    }

    /**
     * Requires the internal auth0 role id, and not just role names
     */
    private Roles mapToAuth0Roles(Set<String> authorities) {
        List<Role> roles = auth0Management.roles();

        return new Roles(authorities
                .stream()
                .map(role -> roles.stream()
                        .filter(r -> r.getName().equals(role))
                        .findFirst()
                        .orElseThrow()
                        .getId())
                .collect(Collectors.toList()));
    }
}
