package com.afakto.service.insurer;

import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromInsurer;
import com.afakto.domain.Contract;
import com.afakto.domain.CreditLimitRequest;
import com.afakto.repository.BuyerFromInsurerRepository;
import com.afakto.repository.BuyerRepository;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.insurer.coface.Request;
import com.afakto.service.insurer.coface.Consult;
import com.afakto.service.insurer.coface.Search;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * This service is used to import data from Insurer's COFACE API.
 * <p></p>
 * API documentation:
 * <a href="https://developers.coface.com/docs-technical?sw=CofaServe%20-%20API%20Product.yaml&swl=API%20CofaServe%20-%20Product#tag/Product">...</a>
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class InsurerCofaceService {
    private final BuyerRepository buyerRepository;
    private final BuyerFromInsurerRepository buyerFromInsurerRepository;
    private final FeignInsurerCofaceService feignInsurerService;

    private final Search search;
    private final Request request;
    private final Consult consult;

    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;

    public String requestCreditLimit(Buyer buyer, BigDecimal amount, String currency) {
        buyer.setCurrency(currency);
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        String insurerCode = Optional.ofNullable(buyer.getBuyerFromInsurer())
            .map(BuyerFromInsurer::getInsurerCode)
            .orElseGet(() -> request.getInsurerCode(header, buyer));

        JsonNode response = request.sendRequestCreditLimit(buyer, header, insurerCode, amount);

        BuyerFromInsurer buyerFromInsurer = Optional.ofNullable(buyer.getBuyerFromInsurer())
            .orElseGet(BuyerFromInsurer::new);

        buyerFromInsurer.setInsurerName("coface");
        buyerFromInsurer.setInsurerCode(response != null
            ? response.get("easyNumber").asText(null)
            : insurerCode);

        buyerFromInsurer = buyerFromInsurerRepository.save(buyerFromInsurer.setBuyer(buyer));
        buyerRepository.save(buyer.setBuyerFromInsurer(buyerFromInsurer));

        return response.get("orderId").asText();
    }

    public int updateCreditLimits(Contract contract) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(contract);

        var covers = feignInsurerService.getProducts(header.apiKey(), header.idToken(), header.policyId())
                .get("companies");

        consult.alreadyImported = new HashMap<>();
        var toSave = StreamSupport.stream(covers.spliterator(), false)
                .map(cover -> {
                    var buyer = consult.findBuyer(contract.getCompany(), cover);
                    if (buyer == null)
                        return null;
                    return consult.setupBuyerFromInsurer(buyer, cover);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        consult.alreadyImported = null;
        log.warn("Saving {} buyers", toSave.size());
        return buyerFromInsurerRepository.saveAll(toSave).size();
    }

    public Buyer updateCreditLimit(Buyer buyer) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        var easyNumber = buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode();
        JsonNode cover = consult.getCover(header, easyNumber, buyer.getCode());

        if (cover == null)
            throw new IllegalArgumentException("Couldn't find cover for buyer");

        if (!buyer.getCode().equals(cover.get("customerReferenceValue").asText(null)))
            consult.patchCustomerReference(header, easyNumber, buyer.getCode());
        var toSave = consult.setupBuyerFromInsurer(buyer, cover);
        if (toSave == null)
            return buyer;
        return buyer.setBuyerFromInsurer(buyerFromInsurerRepository.save(toSave));
    }

    public CreditLimitRequest updateInsurerDecision(CreditLimitRequest creditLimitRequest) {
        log.info("InsurerDecision update");
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(creditLimitRequest.getBuyer());

        var easyNumber = creditLimitRequest.getBuyer().getBuyerFromInsurer()  == null ? null :creditLimitRequest.getBuyer().getBuyerFromInsurer().getInsurerCode();
        JsonNode insurerDecision = feignInsurerService.getDeliveryDecisionForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), easyNumber, creditLimitRequest.getOrderCode());

        if (insurerDecision == null)
            throw new IllegalArgumentException("Couldn't find insurer decision for credit limit code " + creditLimitRequest.getOrderCode());

        JsonNode creditLimitPeriods = insurerDecision.path("product").path("creditLimitPeriods");
        JsonNode firstPeriod = creditLimitPeriods.get(0);
        JsonNode reasonCodeNode = firstPeriod
            .path("creditPeriodCondition")
            .path("reasonCodes")
            .get(0)
            .path("code");

        var code = reasonCodeNode.asText(null);

        if (code == null)
            throw new IllegalArgumentException("Missing 'code' field");
        var insurerComments = firstPeriod.path("underwriterComments");
        if (insurerComments != null && insurerComments.has(0))
            creditLimitRequest.setInsurerComment(insurerComments.get(0).asText(null));
        creditLimitRequest.setInsurerDecision(code);
        return creditLimitRequest;
    }

    public List<BuyerDTO> searchBuyer(BuyerDTO buyer) {
        log.debug("COFACE credit insurance policy found, now searching");
        List<BuyerDTO> buyers = new ArrayList<>();

        JsonNode node = search.search(buyer);

        if (node == null || !node.has("companies") || node.get("companies").isEmpty())
            return buyers;

        for (JsonNode company : node.get("companies")) {
            BuyerDTO filledBuyer = search.mapToBuyerData(company);
            if (filledBuyer.getNumberType() != null)
                buyers.add(filledBuyer);
        }

        return buyers;
    }

}
