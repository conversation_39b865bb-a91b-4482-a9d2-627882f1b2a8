package com.afakto.service.insurer.coface;

import com.afakto.domain.enumeration.NumberType;
import com.afakto.service.dto.AddressDTO;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static com.afakto.batch.Utils.iso3ToIso2;
import static com.afakto.batch.Utils.mapTo3LetterCode;

@RequiredArgsConstructor
@Service
@Slf4j
public class Search {

    private final FeignInsurerCofaceService feignInsurerService;
    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;
    private final CofaceNumberTypeMapping cofaceNumberTypeMapping;
    private final ObjectMapper objectMapper;

    public JsonNode search(BuyerDTO buyer) {
        String countryCode = mapTo3LetterCode(buyer.getAddress().getCountry());
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.fromTest();

        JsonNode result = null;
        try {
            if (buyer.getBuyerFromInsurer() != null && buyer.getBuyerFromInsurer().getInsurerCode() != null)
                result = feignInsurerService.getProductsForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), buyer.getBuyerFromInsurer().getInsurerCode());
            if (buyer.getNumberType() != null && cofaceNumberTypeMapping.getIntoCofaceNumberType().getOrDefault(countryCode, new HashMap<>()).containsKey(buyer.getNumberType())) {
                String cofaceIdentifier = cofaceNumberTypeMapping.getIntoCofaceNumberType().get(countryCode).get(buyer.getNumberType());
                result = feignInsurerService.getBuyerFromIdentifier(header.apiKey(), header.idToken(), header.policyId(), countryCode, cofaceIdentifier, buyer.getNumber());
            }
            result = feignInsurerService.getBuyerFromCompanyName(header.apiKey(), header.idToken(), header.policyId(), countryCode, buyer.getName());
        } catch (FeignException exception) {
            log.warn(getCofaceErrorCode(exception.getMessage()));
        }

        return result;
    }

    public BuyerDTO mapToBuyerData(JsonNode node) {
        BuyerDTO buyer = new BuyerDTO();
        var companyDetails = node.get("headOffice");
        AddressDTO addressDTO = getAddress(companyDetails);
        updateIdentifier(node, buyer);
        return buyer.setAddress(addressDTO).setName(companyDetails.get("name").asText());
    }

    private void updateIdentifier(JsonNode node, BuyerDTO buyer) {
        JsonNode mainIdentifier = node.get("mainIdentifier");
        if (mainIdentifier != null && mainIdentifier.has("value")) {
            if (setBuyerIdentifier(buyer, mainIdentifier))
                return;
        }

        JsonNode secondaryIdentifiers = node.get("secondaryIdentifiers");
        if (secondaryIdentifiers == null || !secondaryIdentifiers.isArray() || secondaryIdentifiers.isEmpty())
            return;
        for (JsonNode secondaryIdentifier : secondaryIdentifiers) {
            if (setBuyerIdentifier(buyer, secondaryIdentifier))
                return;
        }
    }

    private boolean setBuyerIdentifier(BuyerDTO buyer, JsonNode identifierNode) {
        String typeKey = identifierNode.get("type").asText();
        NumberType numberType = cofaceNumberTypeMapping.getFromCofaceNumberType().get(typeKey);
        if (numberType == null)
            return false;
        String value = identifierNode.get("value").asText();
        buyer.setNumber(value).setNumberType(numberType);
        return true;
    }

    private AddressDTO getAddress(JsonNode companyDetails) {
        var address = companyDetails.get("address");
        return new AddressDTO()
            .setCity(address.get("city").asText())
            .setCountry(iso3ToIso2(address.get("countryCode").asText()).toLowerCase())
            .setPostalCode(address.get("postalCode").asText())
            .setStreetName(address.has("streets") && address.get("streets").isArray() && !address.get("streets").isEmpty() ? address.get("streets").get(0).asText() : null);
    }

    private String getCofaceErrorCode(String message) {
        int startIndex = message.indexOf("[{");
        int endIndex = message.lastIndexOf("}]") + "}]".length();

        if (startIndex == -1 || endIndex <= startIndex)
            throw new RuntimeException(message);

        message = message.substring(startIndex, endIndex);
        try {
            JsonNode node = objectMapper.readTree(message).get(0);
            String code = node.get("code").asText();

            if (code.equals("Client403-SOR_BR230236"))
                return "COUNTRY_NOT_ALLOWED";
            if (code.equals("Client412"))
                return "VAT_INVALID";
            return message;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Couldn't parse error message: " + message);
        }
    }

}
