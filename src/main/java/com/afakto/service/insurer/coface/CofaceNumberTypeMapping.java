package com.afakto.service.insurer.coface;

import com.afakto.domain.enumeration.NumberType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Getter
@Component
public class CofaceNumberTypeMapping {

    private final Map<String, NumberType> fromCofaceNumberType = new HashMap<>();
    private final Map<String, Map<NumberType, String>> intoCofaceNumberType = new HashMap<>();

    @PostConstruct
    public void loadNumberTypeMapping() {
        loadFromCofaceNumberType();
        loadIntoCofaceNumberType();
    }

    private void loadFromCofaceNumberType() {
        try (InputStream input = getClass().getResourceAsStream("/buyer/FromCofaceNumberType.json")) {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> raw = mapper.readValue(input, new TypeReference<>() {});
            raw.forEach((key, value) -> {
                try {
                    fromCofaceNumberType.put(key, NumberType.valueOf(value));
                } catch (IllegalArgumentException e) {
                    throw new RuntimeException("Invalid NumberType value in JSON: " + value, e);
                }
            });
        } catch (Exception e) {
            throw new RuntimeException("Failed to load FromCofaceNumberType.json", e);
        }
    }

    private void loadIntoCofaceNumberType() {
        try (InputStream input = getClass().getResourceAsStream("/buyer/IntoCofaceNumberType.json")) {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> raw = mapper.readValue(input, new TypeReference<>() {});
            raw.forEach((key, value) -> {
                try {
                    String[] rawKey = key.split(":");
                    String countryCode = rawKey[0];
                    NumberType numberType = NumberType.valueOf(rawKey[1]);
                    if (!intoCofaceNumberType.containsKey(countryCode))
                        intoCofaceNumberType.put(countryCode, new HashMap<>());
                    intoCofaceNumberType.get(countryCode).put(numberType, value);
                } catch (IllegalArgumentException e) {
                    throw new RuntimeException("Invalid NumberType value in JSON: " + value, e);
                }
            });
        } catch (Exception e) {
            throw new RuntimeException("Failed to load IntoCofaceNumberType.json", e);
        }
    }
}
