package com.afakto.service.insurer.coface;

import com.afakto.domain.*;
import com.afakto.domain.enumeration.CreditLimitRequestStatus;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CreditLimitRequestRepository;
import com.afakto.service.CommentService;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.StreamSupport;

@RequiredArgsConstructor
@Service
@Slf4j
public class Consult {

    private final BuyerRepository buyerRepository;
    private final FeignInsurerCofaceService feignInsurerService;
    private final CreditLimitRequestRepository creditLimitRequestRepository;
    private final CommentService commentService;
    private final ObjectMapper objectMapper;
    public Map<String, BuyerFromInsurer> alreadyImported;

    public JsonNode getCover(FeignInsurerCofaceHeader header, String easyNumber, String customerReference) {
        JsonNode cover;

        var getProductsForBuyer = feignInsurerService.getProductsForBuyer(header.apiKey(), header.idToken(), header.policyId(), customerReference);
        if (getProductsForBuyer != null && getProductsForBuyer.has("companies")
            && !getProductsForBuyer.get("companies").isEmpty())
            cover = getProductsForBuyer.get("companies").get(0);
        else if (easyNumber == null)
            return null;
        else
            cover = feignInsurerService.getProductsForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), easyNumber)
                .get("companies").get(0);

        return cover;
    }

    private void updateProduct(CreditLimitRequest creditLimitRequest, JsonNode product) {
        log.debug("updating creditLimitRequest: [id:{}, orderCode:{}, requestedAmount:{}, amount:{}] with COFACE product: {}", creditLimitRequest.getId(), creditLimitRequest.getOrderCode(), creditLimitRequest.getRequestedAmount(), creditLimitRequest.getAmount(), product.toString());
        switch (product.get("deliveryStatus").asText().toUpperCase()) {
            case "DECIDED_FULL":
                creditLimitRequest
                    .setStatus(CreditLimitRequestStatus.ACCEPTED)
                    .setAmount(product.get("position").get("amount").get("value").decimalValue())
                    .setOrderCode(product.get("deliveryId").asText(null));
                break;
            case "DECIDED_PARTIAL":
                creditLimitRequest
                    .setStatus(CreditLimitRequestStatus.PARTIALLY_ACCEPTED)
                    .setAmount(product.get("position").get("amount").get("value").decimalValue())
                    .setOrderCode(product.get("deliveryId").asText(null));
                break;
            case "DECIDED_REFUSED":
                creditLimitRequest
                    .setStatus(CreditLimitRequestStatus.REJECTED)
                    .setAmount(BigDecimal.ZERO)
                    .setOrderCode(product.get("deliveryId").asText(null));
                break;
            default:
                log.warn("Unknown deliveryStatus: {}", product.get("deliveryStatus").asText().toUpperCase());
                break;
        }

        log.info("Updating credit limit request: [{}]{}:{} | {}",
            creditLimitRequest.getStatus(),
            creditLimitRequest.getAmount(),
            creditLimitRequest.getRequestedAmount(),
            creditLimitRequest.getCurrency());
        creditLimitRequestRepository.save(creditLimitRequest);
    }

    public BuyerFromInsurer setupBuyerFromInsurer(Buyer buyer, JsonNode cover) {
        if (buyer == null || cover == null) {
            log.warn("Buyer or cover is null: {} {}", buyer, cover);
            return null;
        }
        List<JsonNode> products = StreamSupport.stream(cover.get("products").spliterator(), false)
            .filter(item -> item.has("productCode"))
            .filter(item -> FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT.equals(item.get("productCode").asText())).toList();

        var acceptedProduct = products.stream()
            .filter(item -> item.has("deliveryId") && item.hasNonNull("deliveryId"))
            .findFirst()
            .orElse(null);

        log.info("Importing credit limit for buyer code '{}', insurer code '{}', from cover: {}",
            buyer.getCode(),
            buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode(),
            acceptedProduct);
        if (acceptedProduct == null || !acceptedProduct.has("position") || acceptedProduct.get("position").isNull()
            || !acceptedProduct.get("position").has("amount") || acceptedProduct.get("position").get("amount").isNull())
            return null;

        // Set the credit limit
        if (buyer.getCreditLimit() == null) {
            buyer.setCreditLimit(new CreditLimit());
        }

        var isObsolete = false;
        if (acceptedProduct.has("endDate")
            && !acceptedProduct.get("endDate").isNull()
            && !acceptedProduct.get("endDate").asText().isEmpty()) {
            var endDate = OffsetDateTime.parse(
                acceptedProduct.get("endDate").asText(),
                DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            isObsolete = OffsetDateTime.now().isAfter(endDate);
            log.info("Buyer {} is obsolete: {} because of end date {}",
                buyer.getName(),
                isObsolete,
                acceptedProduct.get("endDate"));
        }

        // Set the related insurer data, to display some kind of report
        var buyerFromInsurer = buyer.getBuyerFromInsurer();
        if (buyerFromInsurer == null)
            buyerFromInsurer = new BuyerFromInsurer().setBuyer(buyer);

        if (alreadyImported != null) {
            if (alreadyImported.containsKey(buyer.getCode())) {
                var msg = "[Coface Sync] Insurer has duplicate data for " + buyer.getCode()
                    + " / " + buyer.getBuyerFromInsurer().getInsurerCode()
                    + " with names:"
                    + "\n1: " + alreadyImported.get(buyer.getCode()).getRaw().get("internationalName")
                    + "\n2: " + cover.get("internationalName").asText();
                log.warn("{}. FIRST: {}; SECOND: {}", msg, alreadyImported.get(buyer.getCode()).getRaw(), cover);
                commentService.notify(buyer, msg);
                return null;
            } else {
                alreadyImported.put(buyer.getCode(), buyerFromInsurer);
            }
        }

        var position = acceptedProduct.get("position").get("amount");
        buyer.getCreditLimit()
            .setAmount(isObsolete ? BigDecimal.ZERO : position.get("value").decimalValue())
            .setCurrency(position.get("currency").asText());
        buyerRepository.save(buyer);

        var coverMap = objectMapper.convertValue(cover, new TypeReference<Map<String, Object>>() {
        });

        // update the product history if the most recent COFACE product corresponds to the current product
        if (products.getFirst() == acceptedProduct) {
            creditLimitRequestRepository.getByBuyerId(buyer.getId())
                .stream()
                .reduce((first, second) -> second)
                .ifPresent(lastRequest -> updateProduct(lastRequest, acceptedProduct));
        }

        return buyerFromInsurer
            .setInsurerCode(cover.get("easyNumber").asText())
            .setInsurerName("coface")
            .setRaw(coverMap);
    }

    public void patchCustomerReference(FeignInsurerCofaceHeader header, String easyNumber, String customerReference) {
        log.debug("patching customer reference value for buyer with new reference: {}", customerReference);
        Map<String, Object> body = Map.of("easyNumber", easyNumber, "customerReference", customerReference);
        feignInsurerService.patchCustomerReference(header.apiKey(), header.idToken(), header.policyId(), body);
    }

    public Buyer findBuyer(Company company, JsonNode cover) {
        String code = cover.path("customerReferenceValue").asText(null);
        if (StringUtils.hasText(code))
            return buyerRepository.findOneByCompanyAndCode(company, code).orElse(null);

        String easyNumber = cover.path("easyNumber").asText(null);
        if (StringUtils.hasText(easyNumber))
            return buyerRepository.findOneByCompanyAndBuyerFromInsurerInsurerCode(company, easyNumber).orElse(null);

        log.warn("No buyer found for cover: {}", cover);
        return null;
    }
}
