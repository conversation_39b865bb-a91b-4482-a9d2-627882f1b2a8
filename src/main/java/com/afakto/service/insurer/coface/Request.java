package com.afakto.service.insurer.coface;

import com.afakto.domain.Buyer;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.StreamSupport;

import static com.afakto.batch.Utils.mapTo3LetterCode;

@RequiredArgsConstructor
@Service
@Slf4j
public class Request {

    private final FeignInsurerCofaceService feignInsurerService;
    private final CofaceNumberTypeMapping cofaceNumberTypeMapping;

    private static final String UNKNOWN_EASYNUMBER = "unknown";
    private static final String CREATE_ACTION_CODE = "CREA";
    private static final String UPDATE_ACTION_CODE = "UPDT";
    private static final String CHANGE_ACTION_CODE = "CHANGE";

    public String getInsurerCode(FeignInsurerCofaceHeader header, Buyer buyer) {
        var buyerFromIdentifier = feignInsurerService.getBuyerFromIdentifier(
            header.apiKey(), header.idToken(), header.policyId(),
            mapTo3LetterCode(buyer.getAddress().getCountry()),
            getNumberType(buyer), buyer.getNumber());
        if (buyerFromIdentifier == null)
            return null;
        if (buyerFromIdentifier.has("companies") && !buyerFromIdentifier.get("companies").isEmpty())
            return buyerFromIdentifier.get("companies").get(0).get("easyNumber").asText();
        return null;
    }

    public JsonNode sendRequestCreditLimit(Buyer buyer, FeignInsurerCofaceHeader header, String insurerCode, BigDecimal amount) {
        Map<String, Object> requestData = prepareRequestData(buyer, header, insurerCode);

        if (requestData == null)
            return null;

        if (insurerCode == null)
            insurerCode = UNKNOWN_EASYNUMBER;

        Map<String, Object> request = buildRequestPayload(buyer, amount, requestData);

        return feignInsurerService.requestCreditLimitUpdate(header.apiKey(), header.idToken(), header.policyId(), insurerCode, request);
    }

    private Map<String, Object> prepareRequestData(Buyer buyer, FeignInsurerCofaceHeader header, String insurerCode) {
        if (UNKNOWN_EASYNUMBER.equals(insurerCode))
            return createUnknownBuyerCreditLimitRequest(buyer);
        return getDeliveryType(header, insurerCode);
    }

    private Map<String, Object> buildRequestPayload(Buyer buyer, BigDecimal amount, Map<String, Object> requestData) {
        Map<String, Object> request = new HashMap<>(Map.of(
            "productCode", FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT,
            "orderDetails", Map.of(
                "amount", amount,
                "currency", buyer.getCurrency()
            )
        ));
        request.putAll(requestData);
        return request;
    }

    private String getNumberType(Buyer buyer) {
        String countryCode = mapTo3LetterCode(buyer.getAddress().getCountry());
        return cofaceNumberTypeMapping.getIntoCofaceNumberType()
            .get(countryCode)
            .getOrDefault(buyer.getNumberType(),  buyer.getNumberType().toString());
    }

    private Map<String, Object> getDeliveryType(FeignInsurerCofaceHeader header, String insurerCode) {
        var productsResponse = feignInsurerService.getProductsForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), insurerCode);
        if (productsResponse == null || !productsResponse.has("companies") || productsResponse.get("companies").isEmpty()) {
            log.warn("No products or companies found for easy number: {}", insurerCode);
            return null;
        }

        JsonNode product = findFirstProduct(productsResponse);

        if (product == null || !product.has("deliveryId")) {
            log.warn("No deliveryId found for easy number: {}", insurerCode);
            return createCreditLimitRequest(CREATE_ACTION_CODE, null);
        }

        if (product.get("deliveryId").isNull()) {
            log.warn("DeliveryId is null for easy number: {}", insurerCode);
            return null;
        }

        String actionCode = FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT.equals(product.get("productCode").asText())
            ? UPDATE_ACTION_CODE
            : CHANGE_ACTION_CODE;
        return createCreditLimitRequest(actionCode, product.get("deliveryId").asText());
    }

    private JsonNode findFirstProduct(JsonNode products) {
        return StreamSupport.stream(products.get("companies").get(0).get("products").spliterator(), false)
            .filter(item -> item.has("productCode"))
            .findFirst()
            .orElse(null);
    }

    private Map<String, Object> createCreditLimitRequest(String actionCode, String deliveryId) {
        if (deliveryId == null)
            return Map.of("actionCode", actionCode);
        return Map.of(
            "actionCode", actionCode,
            "deliveryId", deliveryId
        );
    }

    private Map<String, Object> createUnknownBuyerCreditLimitRequest(Buyer buyer) {
        return Map.of(
            "actionCode", CREATE_ACTION_CODE,
            "unknownDebtor", Map.of(
                "companyName", buyer.getName(),
                "countryCode", mapTo3LetterCode(buyer.getAddress().getCountry()),
                "address", Map.of(
                    "street", buyer.getAddress().getStreetNumber() + buyer.getAddress().getStreetName(),
                    "postalCode", buyer.getAddress().getPostalCode(),
                    "city", buyer.getAddress().getCity()))
        );
    }


}
