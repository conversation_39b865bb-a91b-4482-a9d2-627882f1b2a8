package com.afakto.batch;

import static java.util.Map.entry;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.ItemProcessListener;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.BindException;

import com.afakto.domain.BaseEntity;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.domain.Invoice;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CompanyRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.CommentService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class InvoiceImport {
    private final BuyerRepository buyerRepository;
    private final CommentService commentService;
    private final CompanyRepository companyRepository;
    private final CustomConversionService customConversionService;
    private final InvoiceRepository invoiceRepository;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private DelimitedLineTokenizer lineTokenizer;

    private static final Map<String, String> HEADER_TO_FIELD_MAPPING = Map.ofEntries(
            entry("BUYER_CODE", "buyer.code"),
            entry("COMPANY_CODE", "buyer.company.code"),
            entry("DUE_DATE", "dueDate"),
            entry("INV_DATE", "date"),
            entry("INV_NUMBER", "invoiceNumber"),
            entry("INV_TYPE", "type"),
            entry("AMOUNT", "amount"),
            entry("BALANCE", "balance"),
            entry("CURRENCY", "currency"),
            entry("PAYMENT_METHOD", "paymentMethod"),
            entry("RECONCILIATION_JOURNAL", "reconciliationJournal"));

    public Step createStep(Datastream datastream, Resource resource) {
        var lineNumber = new AtomicInteger(1);
        Set<UUID> importedIds = new HashSet<>();

        return new StepBuilder("importInvoiceStep", jobRepository)
                .<Invoice, Invoice>chunk(1, transactionManager)
                .reader(new FlatFileItemReaderBuilder<Invoice>()
                        .beanMapperStrict(false)
                        .lineMapper(createLineMapper())
                        .linesToSkip(1)
                        .skippedLinesCallback(this::setupHeader)
                        .name("invoiceReader")
                        .resource(resource)
                        .strict(false)
                        .build())
                .processor(line -> processLine(datastream, lineNumber.get(), line))
                .writer(new RepositoryItemWriterBuilder<Invoice>()
                        .repository(invoiceRepository)
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(1000)
                .listener(new ItemReadListener<Invoice>() {
                    @Override
                    public void beforeRead() {
                        lineNumber.incrementAndGet();
                    }

                    @Override
                    public void onReadError(Exception e) {
                        log.warn("Trouble reading line {}", lineNumber);

                        var fff = new DatastreamFailure().setDatastream(datastream);
                        datastream.getFailures().add(fff);
                        if (e instanceof FlatFileParseException ffp) {
                            fff
                                    .setLine(ffp.getLineNumber())
                                    .setMessage(ffp.getCause().getMessage())
                                    .setRaw(ffp.getInput());
                            if (ffp.getCause() instanceof BindException be) {
                                var error = be.getFieldError();
                                fff.setMessage(
                                        "Error on field: " + error.getField()
                                                + ". Rejected value: "
                                                + ObjectUtils.nullSafeToString(error.getRejectedValue()));
                            }
                        } else {
                            fff
                                    .setLine(lineNumber.get())
                                    .setMessage(e.getCause().getMessage())
                                    .setRaw(e.getMessage());
                        }
                    }
                })
                .listener(new ItemProcessListener<BaseEntity, BaseEntity>() {
                    @Override
                    public void afterProcess(BaseEntity before, BaseEntity after) {
                        if (after == null)
                            return;
                        if (after.getId() == null)
                            datastream.incrementInserts();
                        else
                            datastream.incrementUpdates();
                    }

                    @Override
                    public void onProcessError(BaseEntity entity, Exception e) {
                        log.warn("Trouble processing line {}, exception: {}", lineNumber, e.getMessage());
                        datastream.getFailures().add(
                                new DatastreamFailure()
                                        .setDatastream(datastream)
                                        .setLine(lineNumber.get())
                                        .setMessage(e.getMessage())
                                        .setRaw(reconstructInput(entity)));
                    }
                })
                .listener(new ItemWriteListener<BaseEntity>() {
                    @Override
                    public void afterWrite(Chunk<? extends BaseEntity> items) {
                        items.forEach(item -> importedIds.add(item.getId()));
                    }
                })
                .listener(new StepExecutionListener() {
                    @Override
                    public ExitStatus afterStep(StepExecution stepExecution) {
                        // So that existing invoices, not reimported, have their balance reset to 0
                        resetNonImported(datastream, importedIds);
                        return null;
                    }
                })
                .build();
    }

    /**
     * Update remaining org/company invoices, to have their balance at 0
     */
    private void resetNonImported(Datastream datastream, Set<UUID> importedIds) {
        var company = companyRepository.findOneByOrgIdAndCodeIgnoreCase(datastream.getOrgId(), datastream.getPath())
                .orElseThrow(() -> new NoSuchElementException("Company not found: " + datastream.getPath()));

        if (importedIds.isEmpty()) {
            log.info("No invoices imported for org {} and company {}", datastream.getOrgId(), datastream.getPath());
            commentService.notify(company, "[Company Sync] No invoice imported for " + datastream.getPath());
            return;
        }

        var updates = buyerRepository.updateAllBuyersCurrencyAndBalance(company);
        log.info("Updated {} buyers currency and amount", updates);

        log.info("Resetting all balances for org {} and company {}",
                datastream.getOrgId(),
                datastream.getPath());

        var resets = invoiceRepository.resetAllBalancesByOrgIdAndCompanyCode(
                datastream.getOrgId(), datastream.getPath(), importedIds);

        datastream.setDeletes(resets);
    }

    private LineMapper<Invoice> createLineMapper() {
        lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setStrict(false);

        BeanWrapperFieldSetMapper<Invoice> fieldSetMapper = new BeanWrapperFieldSetMapper<>();
        fieldSetMapper.setConversionService(customConversionService.getConversionService());
        fieldSetMapper.setStrict(false);
        fieldSetMapper.setTargetType(Invoice.class);

        DefaultLineMapper<Invoice> defaultLineMapper = new DefaultLineMapper<>();
        defaultLineMapper.setFieldSetMapper(fieldSetMapper);
        defaultLineMapper.setLineTokenizer(lineTokenizer);

        return defaultLineMapper;
    }

    /**
     * Process the skipped line to map the header to the field name.
     *
     * @param headerLine the header line
     */
    private void setupHeader(String headerLine) {
        // Determine and set the delimiter
        String delimiter = headerLine.contains(";") ? ";" : ",";
        lineTokenizer.setDelimiter(delimiter);

        lineTokenizer.setNames(
                Arrays.stream(headerLine.split(delimiter))
                        .map(String::trim)
                        .map(String::toUpperCase)
                        .map(header -> header.replace(' ', '_'))
                        .map(header -> HEADER_TO_FIELD_MAPPING.getOrDefault(header, header))
                        .toArray(String[]::new));
    }

    Invoice processLine(Datastream datastream, int lineNumber, Invoice line) {
        var failure = checkCoherentAmounts(lineNumber, line);
        if (failure != null) {
            datastream.getFailures().add(failure.setDatastream(datastream));
            return null;
        }

        if (line.getCompany() == null)
            throw new NoSuchElementException("Company not found in the file");

        if (line.getBuyer() == null)
            throw new NoSuchElementException("Buyer not found in the file");

        line.setBuyer(
                buyerRepository.findOneByCompanyOrgIdAndCompanyCodeAndCode(
                        datastream.getOrgId(),
                        line.getCompany().getCode(),
                        line.getBuyer().getCode())
                        .orElseThrow(() -> new NoSuchElementException(
                                "Buyer not found: " + line.getBuyer().getCode()
                                        + " (company: " + line.getCompany().getCode() + ")")));

        return invoiceRepository
                .findOneByBuyerAndTypeAndInvoiceNumber(line.getBuyer(), line.getType(), line.getInvoiceNumber())
                .orElseGet(() -> new Invoice()
                        .setBuyer(line.getBuyer())
                        .setInvoiceNumber(line.getInvoiceNumber())
                        .setType(line.getType()))
                .setAmount(line.getAmount())
                .setBalance(line.getBalance())
                .setCurrency(line.getCurrency())
                .setDate(line.getDate())
                .setDueDate(line.getDueDate());
    }

    DatastreamFailure checkCoherentAmounts(int lineNumber, Invoice line) {
        String message = null;
        if (line.getAmount() == null || line.getBalance() == null) {
            message = "Amount and balance must be present";
        } else if (line.getAmount().abs().compareTo(line.getBalance().abs()) < 0) {
            message = "Balance must be less than or equal to amount";
        } else if (InvoiceType.CREDIT_NOTE.equals(line.getType())
                && (line.getAmount().signum() > 0 || line.getBalance().signum() > 0)) {
            // Positive credit notes are refused
            message = "Credit note's amount or balance should be negative";
        } else if (InvoiceType.INVOICE.equals(line.getType())
                && (line.getAmount().signum() < 0 || line.getBalance().signum() < 0)) {
            // Negative invoices are refused
            message = "Invoice's amount or balance should be positive";
        } else if (InvoiceType.UNALLOCATED_PAYMENT.equals(line.getType())
                && (line.getAmount().signum() > 0 || line.getBalance().signum() > 0)) {
            // Positive unallocated payments are refused
            message = "Unallocated payment's amount or balance should be negative";
        } else if (ObjectUtils.isEmpty(line.getCurrency())) {
            message = "Currency must be present";
        } else if (ObjectUtils.isEmpty(line.getDate())) {
            message = "Invoice date must be present";
        } else if (ObjectUtils.isEmpty(line.getDueDate())) {
            message = "Due date must be present";
        }
        if (message != null)
            return new DatastreamFailure()
                    .setLine(lineNumber)
                    .setMessage(message)
                    .setRaw(reconstructInput(line));
        return null;
    }

    private String reconstructInput(BaseEntity base) {
        if (base instanceof Invoice line && line.getBuyer() != null && line.getBuyer().getCompany() != null)
            return Arrays.toString(
                    new Object[] {
                            line.getBuyer().getCompany().getCode(),
                            line.getBuyer().getCode(),
                            line.getType(), line.getInvoiceNumber(),
                            line.getDate(), line.getDueDate(),
                            line.getCurrency(), line.getAmount(), line.getBalance(),
                            line.getPaymentMethod() });
        return "";
    }
}
