package com.afakto.batch.bnp;

import static com.afakto.domain.enumeration.DatastreamType.BNP_REMITTANCE;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Currency;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.data.builder.RepositoryItemReaderBuilder;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import com.afakto.domain.BuyerFromFactor;
import com.afakto.domain.Cession;
import com.afakto.domain.Contract;
import com.afakto.domain.Datastream;
import com.afakto.domain.Invoice;
import com.afakto.domain.enumeration.ActionType;
import com.afakto.domain.enumeration.DatastreamType;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.domain.enumeration.PaymentMethod;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.FileService;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;

/**
 * From the second assignment, this file contains invoices and credit notes
 * created since the previous assignment.
 *
 * It is a datastream.
 *
 * TODO manage the BNP limit:
 *
 * A remittance must not contain more than 3000 entries
 * (invoices or credit notes). If there are
 * more than 3000 invoices (or CN), you have to insert
 * several remittances in the file.
 */
@Component
@RequiredArgsConstructor
public class BnpRemittanceExportJob {
    private final Logger log = LoggerFactory.getLogger(BnpRemittanceExportJob.class);

    @Value("file:${application.filesystem}/out/")
    private Resource toExport;

    private final FileService fileService;
    private final InvoiceRepository invoiceRepository;

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private static final Map<String, Sort.Direction> READER_SORT = new LinkedHashMap<>();
    static {
        READER_SORT.put("type", Sort.Direction.DESC);
        READER_SORT.put("date", Sort.Direction.ASC);
        READER_SORT.put("invoiceNumber", Sort.Direction.ASC);
    }

    private static final DateTimeFormatter REMITTANCE_IDENTIFIER_FORMATTER = DateTimeFormatter.ofPattern("yyMMddHH");

    private static Map<PaymentMethod, Integer> paymentCodes = Map.of(
            PaymentMethod.BANK_CHECK, 1,
            PaymentMethod.DRAFT, 2,
            PaymentMethod.BANK_TRANSFER, 4,
            PaymentMethod.OTHERS, 6,
            PaymentMethod.BAO, 7,
            PaymentMethod.STANDING_ORDER, 8);

    private static class JobContext {
        private Cession cession;

        private int lineNumber;
        private boolean invoicesStarted;
        private boolean creditNotesStarted;

        private int blockCount;
        private long blockSum;
        private int remittanceIdentifier;
    }

    @PostConstruct
    public void init() {
        fileService.init(toExport);
    }

    public void process(Cession cession) throws JobExecutionAlreadyRunningException, JobRestartException,
            JobInstanceAlreadyCompleteException, JobParametersInvalidException, IOException {
        log.info("Exporting BNP remittance file for contract {}", cession.getContract().getContractNumber());

        JobContext context = new JobContext(); // New context per execution
        context.cession = cession;

        jobLauncher.run(
                new JobBuilder("exportBnpRemittanceJob", jobRepository)
                        .start(createStep(context))
                        .build(),
                new JobParametersBuilder()
                        .addDate("date", new Date())
                        .addJobParameter("cession", cession.getId(), UUID.class)
                        .addJobParameter("type", BNP_REMITTANCE, DatastreamType.class)
                        .toJobParameters());

        log.info("File exported successfully");
    }

    Step createStep(JobContext context) throws IOException {
        var contract = context.cession.getContract();
        var datastream = new Datastream()
                .setOrgId(contract.getCompany().getOrgId())
                .setOutgoing(true)
                .setType(DatastreamType.BNP_REMITTANCE)
                .setPath("all")
                .setName(
                        LocalDate.now().toString().replace("-", "")
                                + "_bnp_remittance_"
                                + contract.getContractNumber() + ".txt")
                .setInserts(context.cession.getMetadata().values().stream()
                        .map(metadata -> metadata.get(ActionType.INSERT))
                        .map(entry -> entry[0].intValue()).reduce(0, Integer::sum));

        if (context.cession.getDatastreams() == null) {
            context.cession.setDatastreams(new HashSet<>());
        }
        context.cession.getDatastreams().add(datastream);

        return new StepBuilder("exportBnpRemittanceStep", jobRepository)
                .<Invoice, Invoice>chunk(1, transactionManager)
                .faultTolerant()
                .reader(new RepositoryItemReaderBuilder<Invoice>()
                        .name("bnpRemittanceReader")
                        .repository(invoiceRepository)
                        .methodName("findAllByCessionsToRemit")
                        .arguments(context.cession)
                        .pageSize(1000)
                        // Required so that the export should not "double" some exports when paginating
                        .sorts(READER_SORT)
                        .build())
                .writer(new FlatFileItemWriterBuilder<Invoice>()
                        .encoding("ISO-8859-1") // Set the encoding to ISO-8859-1
                        .name("bnpRemittanceWriter")
                        .resource(getResource(datastream))
                        .shouldDeleteIfExists(true)
                        .headerCallback(writer -> writer.write(headerCallback(context, contract)))
                        .lineAggregator(invoice -> lineAggregator(context, contract, invoice))
                        .footerCallback(writer -> writer.write(footerCallback(context, contract)))
                        .build())
                .build();
    }

    private String headerCallback(JobContext context, Contract contract) {
        // Reset starting data
        context.lineNumber = 1;
        context.invoicesStarted = false;
        context.creditNotesStarted = false;

        return String.format(
                "01%-8.8s%-30.30s%-30.30s%tY%<tm%<td%2s%94s%06d",
                contract.getContractNumber(),
                contract.getCompany().getName(),
                "BNP Paribas Factor",
                LocalDate.now(),
                "AT",
                "",
                context.lineNumber++);
    }

    private String lineAggregator(JobContext context, Contract contract, Invoice invoice) {
        var result = "";

        if (invoice.getType() == InvoiceType.INVOICE) {
            if (!context.invoicesStarted) {
                context.invoicesStarted = true;
                result += startInvoiceBlock(context, contract);
            }
        } else {
            if (context.invoicesStarted) {
                context.invoicesStarted = false;
                result += endInvoiceBlock(context, contract);
            }
            if (!context.creditNotesStarted) {
                context.creditNotesStarted = true;
                result += startCreditNoteBlock(context, contract);
            }
        }

        context.blockCount++;
        context.blockSum += Math.abs(invoice.getAmount()
                .movePointRight(Currency.getInstance(invoice.getCurrency()).getDefaultFractionDigits())
                .longValue());

        var decimals = Currency.getInstance(invoice.getCurrency()).getDefaultFractionDigits();
        return result + String.format(
                "%2s%-8.8s%8d%-9.9s%-24.24s%-27.27s%017d%tY%<tm%<td%tY%<tm%<td%-1.1s%-30.30s%-14.14s%-18.18s%06d",
                invoice.getType() == InvoiceType.CREDIT_NOTE ? "35" : "25",
                contract.getContractNumber(),
                context.remittanceIdentifier,
                Optional.ofNullable(invoice.getBuyer().getBuyerFromFactor())
                        .map(BuyerFromFactor::getFactorCode)
                        .orElse(""),
                invoice.getBuyer().getCode(),
                invoice.getInvoiceNumber(),
                Math.abs(invoice.getAmount().movePointRight(decimals).longValue()),
                invoice.getDate(),
                invoice.getDueDate(),
                paymentCodes.get(Optional.ofNullable(invoice.getPaymentMethod()).orElse(PaymentMethod.BANK_TRANSFER)),
                "",
                invoice.getBuyer().getNumber(),
                "",
                context.lineNumber++);
    }

    private String footerCallback(JobContext context, Contract contract) {
        var result = "";

        if (context.creditNotesStarted) {
            result += endCreditNoteBlock(context, contract);
        } else if (context.invoicesStarted) {
            result += endInvoiceBlock(context, contract);
        }

        return result + String.format(
                "98%-8.8s%-30.30s%-30.30s%tY%<tm%<td%96s%06d%n",
                contract.getContractNumber(),
                contract.getCompany().getName(),
                "BNP Paribas Factor",
                LocalDate.now(),
                "",
                context.lineNumber++);
    }

    private String startInvoiceBlock(JobContext context, Contract contract) {
        context.blockCount = 0;
        context.blockSum = 0;
        context.remittanceIdentifier = Integer.valueOf(LocalDateTime.now().format(REMITTANCE_IDENTIFIER_FORMATTER));

        return String.format(
                "20%-8.8s%8d%tY%<tm%<td%2s%3s%1d%142s%06d%n",
                contract.getContractNumber(),
                context.remittanceIdentifier,
                LocalDate.now(),
                contract.getCountry().toUpperCase(),
                contract.getFinancialInformation().getCurrency(),
                Currency.getInstance(contract.getFinancialInformation().getCurrency()).getDefaultFractionDigits(),
                "",
                context.lineNumber++);
    }

    private String endInvoiceBlock(JobContext context, Contract contract) {
        return String.format(
                "29%-8.8s%8d%tY%<tm%<td%2s%3s%1d%9s%05d%4s%017d%107s%06d%n",
                contract.getContractNumber(),
                context.remittanceIdentifier,
                LocalDate.now(),
                contract.getCountry().toUpperCase(),
                contract.getFinancialInformation().getCurrency(),
                Currency.getInstance(contract.getFinancialInformation().getCurrency()).getDefaultFractionDigits(),
                "",
                context.blockCount,
                "",
                context.blockSum,
                "",
                context.lineNumber++);
    }

    private String startCreditNoteBlock(JobContext context, Contract contract) {
        context.blockCount = 0;
        context.blockSum = 0;
        context.remittanceIdentifier = Integer.valueOf(LocalDateTime.now().format(REMITTANCE_IDENTIFIER_FORMATTER))
                + 24;

        return String.format(
                "30%-8.8s%8d%tY%<tm%<td%2s%3s%1d%142s%06d%n",
                contract.getContractNumber(),
                context.remittanceIdentifier,
                LocalDate.now(),
                contract.getCountry().toUpperCase(),
                contract.getFinancialInformation().getCurrency(),
                Currency.getInstance(contract.getFinancialInformation().getCurrency()).getDefaultFractionDigits(),
                "",
                context.lineNumber++);
    }

    private String endCreditNoteBlock(JobContext context, Contract contract) {
        return String.format(
                "39%-8.8s%8d%tY%<tm%<td%2s%3s%1d%9s%05d%4s%017d%107s%06d%n",
                contract.getContractNumber(),
                context.remittanceIdentifier,
                LocalDate.now(),
                contract.getCountry().toUpperCase(),
                contract.getFinancialInformation().getCurrency(),
                Currency.getInstance(contract.getFinancialInformation().getCurrency()).getDefaultFractionDigits(),
                "",
                context.blockCount,
                "",
                context.blockSum,
                "",
                context.lineNumber++);
    }

    private FileSystemResource getResource(Datastream datastream) throws IOException {
        String path = datastream.getOrgId() + "/bnp_remittance/" + datastream.getPath();

        toExport.getFile().toPath().resolve(path).toFile().mkdirs();

        return new FileSystemResource(
                toExport.getFile().toPath().resolve(path + "/" + datastream.getName()));
    }
}
