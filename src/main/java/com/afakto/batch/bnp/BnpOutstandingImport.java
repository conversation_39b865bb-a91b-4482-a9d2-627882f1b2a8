package com.afakto.batch.bnp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.ItemProcessListener;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.JobInterruptedException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.UnexpectedJobExecutionException;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.PatternMatchingCompositeLineMapper;
import org.springframework.batch.item.file.transform.FixedLengthTokenizer;
import org.springframework.batch.item.file.transform.Range;
import org.springframework.batch.item.file.transform.RangeArrayPropertyEditor;
import org.springframework.batch.item.support.builder.ClassifierCompositeItemWriterBuilder;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.core.io.Resource;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;

import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromFactor;
import com.afakto.domain.Contract;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.domain.InvoiceFromFactor;
import com.afakto.repository.BuyerFromFactorRepository;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.ContractRepository;
import com.afakto.repository.InvoiceFromFactorRepository;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.CommentService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class BnpOutstandingImport {
    private static final String _UNKNOWN_BUYER_NUMBER = "9PRO00000";

    private final BuyerFromFactorRepository buyerFromFactorRepository;
    private final BuyerRepository buyerRepository;
    private final CommentService commentService;
    private final ContractRepository contractRepository;
    private final InvoiceFromFactorRepository invoiceFromFactorRepository;
    private final InvoiceRepository invoiceRepository;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    @SuppressWarnings({ "unchecked", "rawtypes" })
    public Step createStep(Datastream datastream, Resource resource) {
        var lineNumber = new AtomicInteger(1);
        Set<UUID> importedIds = new HashSet<>();
        Set<Buyer> unknownBuyers = new HashSet<>();

        var contract = contractRepository
                .findOneByCompanyOrgIdAndContractNumber(datastream.getOrgId(), datastream.getPath())
                .orElseThrow();

        SimpleStepBuilder result = new StepBuilder("importOutstandingStep", jobRepository)
                .<BaseEntity, BaseEntity>chunk(1, transactionManager)
                .reader(new FlatFileItemReaderBuilder<BaseEntity>()
                        .beanMapperStrict(false)
                        .lineMapper(createLineMapper())
                        .linesToSkip(1)
                        .skippedLinesCallback(headerLine -> checkHeader(datastream, headerLine))
                        .name("outstandingReader")
                        .resource(resource)
                        .strict(false)
                        .build())
                .processor(item -> {
                    if (item instanceof BuyerFromFactor buyerFromFactor)
                        item = processBuyer(datastream, lineNumber.get(), unknownBuyers, contract, buyerFromFactor);

                    if (item instanceof InvoiceFromFactor invoiceFromFactor)
                        item = processInvoice(datastream, lineNumber.get(), unknownBuyers, contract, invoiceFromFactor);

                    return item;
                })
                .writer(new ClassifierCompositeItemWriterBuilder()
                        .classifier(item -> {
                            if (item instanceof BuyerFromFactor)
                                return new RepositoryItemWriterBuilder<BuyerFromFactor>()
                                        .repository(buyerFromFactorRepository)
                                        .build();
                            if (item instanceof InvoiceFromFactor)
                                return new RepositoryItemWriterBuilder<InvoiceFromFactor>()
                                        .repository(invoiceFromFactorRepository)
                                        .build();
                            return null;
                        })
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(IncorrectResultSizeDataAccessException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(10000)
                .listener(new ItemReadListener<BaseEntity>() {
                    @Override
                    public void beforeRead() {
                        lineNumber.incrementAndGet();
                    }
                })
                .listener(new ItemProcessListener<BaseEntity, BaseEntity>() {
                    @Override
                    public void afterProcess(BaseEntity before, BaseEntity after) {
                        if (after == null)
                            return;
                        if (after.getId() == null)
                            datastream.incrementInserts();
                        else
                            datastream.incrementUpdates();
                    }

                    @Override
                    public void onProcessError(BaseEntity entity, Exception e) {
                        log.warn("Trouble processing line {}, exception: {}", lineNumber, e.getMessage());
                        datastream.getFailures().add(
                                new DatastreamFailure()
                                        .setDatastream(datastream)
                                        .setLine(lineNumber.get())
                                        .setMessage(e.getMessage()));
                    }
                })
                .listener(new ItemWriteListener<BaseEntity>() {
                    @Override
                    public void afterWrite(Chunk<? extends BaseEntity> items) {
                        items.forEach(item -> {
                            if (item instanceof BuyerFromFactor)
                                importedIds.add(item.getId());
                        });
                    }
                });
        result.listener(new StepExecutionListener() {
            @Override
            public ExitStatus afterStep(StepExecution stepExecution) {
                // So that existing invoices, not reimported, have their balance reset to 0
                resetNonImported(contract, datastream, importedIds);
                resetUnknowns(contract, unknownBuyers);
                return null;
            }
        });
        return result.build();
    }

    /**
     * Update remaining org/company buyers, to have their factor data reset to 0
     */
    private void resetNonImported(Contract contract, Datastream datastream, Set<UUID> importedIds) {
        if (importedIds.isEmpty())
            return;

        log.info("Resetting all buyer from factor data for org {}, company {} and currency {}. {} buyers imported.",
                datastream.getOrgId(),
                contract.getCompany().getCode(),
                contract.getFinancialInformation().getCurrency(),
                importedIds.size());

        var resets = buyerRepository.resetAllBalancesByOrgIdAndCompanyCodeAndCurrency(
                datastream.getOrgId(),
                contract.getCompany().getCode(),
                contract.getFinancialInformation().getCurrency(),
                importedIds);

        datastream.setDeletes(resets);
    }

    /**
     * To follow which buyer are not known by the factor
     */
    private void resetUnknowns(Contract contract, Set<Buyer> unknownBuyers) {
        if (unknownBuyers.isEmpty())
            return;

        unknownBuyers.forEach(b -> commentService.notify(b, "[BNP Sync] Unknown buyer:\n%s".formatted(b.getName())));
        var resets = buyerRepository.updateBuyerFromFactorUnknownByCompanyAndBuyers(
                contract.getCompany(),
                unknownBuyers);

        log.info("Unknown buyers reset on {} items", resets);
    }

    /**
     * Process the skipped line to map the header to the field name.
     *
     * @param headerLine the header line
     * @throws JobInterruptedException
     */
    private void checkHeader(Datastream datastream, String headerLine) {
        if (headerLine == null || !headerLine.contains("V2.0"))
            return;

        datastream.setError("V2.0 file detected. Skipping import.");

        throw new UnexpectedJobExecutionException("V2.0 file detected. Skipping step.");
    }

    /*
     * This is the more complex part, that tokenizes the lines and maps them to the
     * target objects
     */
    private PatternMatchingCompositeLineMapper<BaseEntity> createLineMapper() {
        var buyerGlobalRange = new RangeArrayPropertyEditor();
        buyerGlobalRange.setAsText("11-19,25,65,68,69,86,103,120,137-153");
        var buyerGlobalTokenizer = new FixedLengthTokenizer();
        buyerGlobalTokenizer.setColumns((Range[]) buyerGlobalRange.getValue());
        buyerGlobalTokenizer.setNames("number", "name", "currency", "decimals",
                "amountApproved",
                "amountOutstanding", "amountUnSecured", "amountUnFunded", "amountDraftReceived");
        buyerGlobalTokenizer.setStrict(false);

        var buyerRange = new RangeArrayPropertyEditor();
        buyerRange.setAsText("11-19,25,65,68,69,86,103,120,137,146,181,186,216,265");
        var buyerTokenizer = new FixedLengthTokenizer();
        buyerTokenizer.setColumns((Range[]) buyerRange.getValue());
        buyerTokenizer.setNames("number", "name", "currency", "decimals",
                "amountOutstanding", "amountUnSecured", "amountUnFunded", "amountDraftReceived",
                "factorCode", "code", "zipcode", "city");
        buyerTokenizer.setStrict(false);

        var invoiceRange = new RangeArrayPropertyEditor();
        invoiceRange.setAsText("3,12,47,82,90,98,101,102-118,136-152,153,170,178-194,195-211,212-228,229-245");
        var invoiceTokenizer = new FixedLengthTokenizer();
        invoiceTokenizer.setColumns((Range[]) invoiceRange.getValue());
        invoiceTokenizer.setNames("invoice.buyer.buyerFromFactor.factorCode", "invoice.buyer.code", "invoiceNumber",
                "date", "dueDate",
                "currency", "decimals",
                "amount", "balance", "amountDraftReceived", "draftDueDate",
                "amountFunded", "amountUnfunded", "amountSecured", "amountUnsecured");
        invoiceTokenizer.setStrict(false);

        var buyerMapper = new BeanWrapperFieldSetMapper<BaseEntity>();
        buyerMapper.setTargetType(BuyerFromFactor.class);
        buyerMapper.setStrict(false);
        var invoiceMapper = new BeanWrapperFieldSetMapper<BaseEntity>();
        invoiceMapper.setTargetType(InvoiceFromFactor.class);
        invoiceMapper.setStrict(false);

        DefaultConversionService conversionService = new DefaultConversionService();
        DefaultConversionService.addDefaultConverters(conversionService);
        conversionService.addConverter(new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String text) {
                if (ObjectUtils.isEmpty(text))
                    return null;
                return LocalDate.parse(text, DateTimeFormatter.BASIC_ISO_DATE);
            }
        });
        // Remove leading 0, which appear "before" the - negative sign
        conversionService.addConverter(new Converter<String, BigDecimal>() {
            @Override
            public BigDecimal convert(String text) {
                return new BigDecimal(StringUtils.stripStart(text, "0"));
            }
        });
        buyerMapper.setConversionService(conversionService);
        invoiceMapper.setConversionService(conversionService);

        var lineMapper = new PatternMatchingCompositeLineMapper<BaseEntity>();
        lineMapper.setTokenizers(Map.of(
                "10*", buyerGlobalTokenizer,
                "20*", buyerTokenizer,
                "30*", invoiceTokenizer,
                "*", fieldSet -> {
                    log.warn("Fieldset: {}", fieldSet);
                    return null;
                }));
        lineMapper.setFieldSetMappers(Map.of(
                "10*", buyerMapper,
                "20*", buyerMapper,
                "30*", invoiceMapper,
                "*", fieldSet -> {
                    log.warn("Fieldset: {}", fieldSet);
                    return null;
                }));

        return lineMapper;
    }

    private Buyer findBuyerByNumber(Datastream datastream, Contract contract, BuyerFromFactor buyerFromFactor) {
        Buyer buyer = null;
        log.debug("Buyer from factor named {} with number: {}",
                buyerFromFactor.getName(),
                buyerFromFactor.getNumber());
        try {
            buyer = buyerRepository
                    .findOneByCompanyAndNumber(contract.getCompany(), buyerFromFactor.getNumber())
                    .or(() -> buyerRepository.findOneByCompanyAndBuyerFromFactorNumberAndBuyerFromFactorCurrency(
                            contract.getCompany(), buyerFromFactor.getNumber(), buyerFromFactor.getCurrency()))
                    .orElseThrow(() -> new NoSuchElementException("Buyer not found: " + buyerFromFactor.getNumber()
                            + ", factor code: " + buyerFromFactor.getFactorCode()
                            + " (company: " + contract.getCompany().getCode() + ")"));
            buyerFromFactor.setCode(buyer.getCode());
        } catch (IncorrectResultSizeDataAccessException e) {
            // Important, to have the proper approved amount
            buyerFromFactor.calculateDecimals();

            var updates = buyerRepository.updateAmountApproved(
                    contract.getCompany(),
                    buyerFromFactor.getNumber(),
                    buyerFromFactor.getAmountApproved());
            if (updates < 2)
                updates = buyerRepository.updateAmountApproved2(
                        contract.getCompany(),
                        buyerFromFactor.getNumber(),
                        buyerFromFactor.getCurrency(),
                        buyerFromFactor.getAmountApproved());
            datastream.setUpdates(datastream.getUpdates() + updates);
            log.warn("{} buyers with same number present: {}, updating amountApproved to {} {}",
                    updates,
                    buyerFromFactor.getNumber(),
                    buyerFromFactor.getAmountApproved(),
                    buyerFromFactor.getCurrency());
            return null;
        }
        log.debug("Found buyer {} with code: {} and approved amount: {}", buyer.getName(), buyer.getCode(),
                buyerFromFactor.getAmountApproved());

        return buyer;
    }

    private Buyer findBuyerByCode(Datastream datastream, Contract contract, BuyerFromFactor buyerFromFactor) {
        Buyer buyer = null;

        log.debug("Buyer from factor named {} with code: {}",
                buyerFromFactor.getName(),
                buyerFromFactor.getCode());
        buyer = buyerRepository
                .findOneByCompanyAndCode(contract.getCompany(), buyerFromFactor.getCode())
                .orElseThrow(() -> new NoSuchElementException("Buyer not found: " + buyerFromFactor.getCode()
                        + " (company: " + contract.getCompany().getCode() + ")"));
        // This is a hack, reflecting the presence of SIREN **and** SIRET
        if (buyerFromFactor.getNumber() == null)
            buyerFromFactor.setNumber(buyer.getNumber());

        return buyer;
    }

    BaseEntity processBuyer(Datastream datastream, int lineNumber, Set<Buyer> unknownBuyers, Contract contract,
            BuyerFromFactor buyerFromFactor) {
        Buyer buyer = null;

        if (_UNKNOWN_BUYER_NUMBER.equals(buyerFromFactor.getNumber())) {
            if (!unknownBuyers.isEmpty()) {
                log.info("{} => {} buyers", buyerFromFactor.getName(), unknownBuyers.size());
                datastream.getFailures().add(new DatastreamFailure()
                        .setDatastream(datastream)
                        .setLine(lineNumber)
                        .setMessage(String.format("%s => %d buyers", buyerFromFactor.getName(), unknownBuyers.size())));
            }
        } else if (buyerFromFactor.getCode() == null) {
            // This is for lines «10» => agrément
            // 10013206709PROAC3CU WAL-MART CANADA CORP
            // CAD20000000014000000000000000112584351000000000000000000000000003251272400000000000000000
            // 000126
            buyer = findBuyerByNumber(datastream, contract, buyerFromFactor);
        } else if (buyerFromFactor.getCode().isEmpty()) {
            datastream.getFailures().add(new DatastreamFailure()
                    .setDatastream(datastream)
                    .setLine(lineNumber)
                    .setMessage("Buyer code is required"));
        } else {
            // This is for lines «20» => buyer
            buyer = findBuyerByCode(datastream, contract, buyerFromFactor);
        }

        if (buyer == null)
            return null;

        if (buyer.getBuyerFromFactor() != null) {
            buyerFromFactor
                    .setFactorCode(buyer.getBuyerFromFactor().getFactorCode())
                    .setId(buyer.getBuyerFromFactor().getId())
                    .setVersion(buyer.getBuyerFromFactor().getVersion());
        }

        return buyerFromFactor
                .calculateDecimals()
                .setBuyer(buyer);
    }

    private BaseEntity processInvoice(Datastream datastream, int lineNumber, Set<Buyer> unknownBuyers,
            Contract contract, InvoiceFromFactor invoiceFromFactor) {
        var invoice = invoiceRepository
                .findOneByBuyerCompanyAndBuyerCodeAndInvoiceNumberAndAmountSign(
                        contract.getCompany(),
                        invoiceFromFactor.getInvoice().getBuyer().getCode(),
                        invoiceFromFactor.getInvoiceNumber(),
                        invoiceFromFactor.getAmount())
                .or(() -> invoiceRepository.findOneByBuyerCompanyAndInvoiceNumberAndAmountSign(
                        contract.getCompany(),
                        invoiceFromFactor.getInvoiceNumber(),
                        invoiceFromFactor.getAmount()))
                .orElseThrow(
                        () -> new NoSuchElementException(
                                "Invoice not found: " + invoiceFromFactor.getInvoiceNumber()
                                        + " (company: " + contract.getCompany().getCode()
                                        + ", buyer: " + invoiceFromFactor.getInvoice().getBuyer().getCode() + ")"));
        log.debug("Invoice {} funded {} {}",
                invoiceFromFactor.getInvoiceNumber(),
                invoiceFromFactor.getAmountFunded(),
                invoiceFromFactor.getCurrency());

        if (ObjectUtils.isEmpty(invoiceFromFactor.getInvoice().getBuyer().getCode()))
            unknownBuyers.add(invoice.getBuyer());

        if (invoice.getInvoiceFromFactor() != null) {
            // To properly overwrite the existing entity
            invoiceFromFactor
                    .setId(invoice.getInvoiceFromFactor().getId())
                    .setVersion(invoice.getInvoiceFromFactor().getVersion());
        }

        return invoiceFromFactor.calculateDecimals()
                .setInvoice(invoice)
                .setType(invoice.getType());
    }
}
